# Saga 分布式事务系统 - Docker Compose 配置
# 轻量级开发配置版本

version: '3.8'

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: saga-mysql-light
    restart: unless-stopped
    
    # 资源限制 - 轻量级配置
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    
    # 端口映射
    ports:
      - "3306:3306"
    
    # 环境变量
    environment:
      MYSQL_ROOT_PASSWORD: 12345678a
      MYSQL_DATABASE: saga
      MYSQL_USER: saga_user
      MYSQL_PASSWORD: saga_pass
    
    # 数据卷
    volumes:
      - mysql_data_light:/var/lib/mysql
      - ./manifest/config/mysql/light.cnf:/etc/mysql/conf.d/light.cnf:ro
      - ./manifest/config/sql:/docker-entrypoint-initdb.d:ro
    
    # MySQL 启动参数
    command: >
      --innodb-buffer-pool-size=12G
      --innodb-log-file-size=2G
      --max-connections=500
      --innodb-flush-log-at-trx-commit=2
      --innodb-flush-method=O_DIRECT
      --performance-schema=ON
    
    # 健康检查
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p12345678a"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Saga 应用服务
  saga-app:
    image: saga-transaction-system:latest
    container_name: saga-app-light
    restart: unless-stopped
    
    # 资源限制 - 轻量级配置
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
    
    # 端口映射
    ports:
      - "8080:8080"
    
    # 环境变量
    environment:
      - GOMAXPROCS=4
      - GOGC=100
      - GOMEMLIMIT=8GiB
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=saga
      - DB_USER=root
      - DB_PASS=12345678a

    # 配置文件挂载
    volumes:
      - ./manifest/config/config-docker.yaml:/app/config.yaml:ro

    # 启动命令
    command: ["./saga", "--config=/app/config.yaml"]
    
    # 依赖关系
    depends_on:
      mysql:
        condition: service_healthy
    
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# 数据卷定义
volumes:
  mysql_data_light:
    driver: local

# 网络定义
networks:
  default:
    driver: bridge
