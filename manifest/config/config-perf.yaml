# Saga 分布式事务服务配置 (性能测试环境)
saga:
  # 补偿恢复服务配置
  compensation:
    recovery:
      enabled: true               # 性能测试环境启用补偿恢复服务
      taskTimeout: "5m"           # 性能测试环境设置较长的超时时间
      recoveryInterval: "30s"     # 性能测试环境设置合理的检查间隔
      maxConcurrentTasks: 10      # 性能测试环境设置更多的并发数

    # 回滚配置
    rollback:
      maxRetries: 3               # 性能测试环境设置合理的重试次数
      timeout: "30s"              # 性能测试环境设置较长的超时时间

# 数据库配置
database:
  default:
    link: "mysql:root:12345678a@tcp(127.0.0.1:3306)/saga"
    debug: false                  # 性能测试时关闭调试模式
    maxIdle: 10                   # 最大空闲连接数
    maxOpen: 100                  # 最大打开连接数
    maxLifetime: "1h"             # 连接最大生存时间
    
# 服务器配置
server:
  address: ":8080"
  readTimeout: "30s"              # 读取超时
  writeTimeout: "30s"             # 写入超时
  idleTimeout: "60s"              # 空闲超时
  maxHeaderBytes: 1048576         # 最大请求头大小 (1MB)
  
# 日志配置
logger:
  level: "info"                   # 性能测试时使用 info 级别
  stdout: true
  
# 性能监控配置
pprof:
  enabled: true                   # 启用性能分析
  address: ":6060"                # pprof 监听地址

# Go 运行时配置
runtime:
  gomaxprocs: 8                   # 设置 GOMAXPROCS
  gogc: 50                        # 设置 GC 目标百分比
  gomemlimit: "16GiB"             # 设置内存限制
