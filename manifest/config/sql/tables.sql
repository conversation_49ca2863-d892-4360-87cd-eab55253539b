CREATE DATABASE IF NOT EXISTS saga
    DEFAULT CHARACTER SET = 'utf8mb4';

use saga;

CREATE TABLE IF NOT EXISTS saga_transactions (
  saga_id         CHAR(36)     NOT NULL PRIMARY KEY COMMENT 'Saga 事务唯一 ID（UUID）',
  name            VARCHAR(64)  NOT NULL DEFAULT '' COMMENT 'Saga 名称',
  saga_status     ENUM('pending', 'running', 'completed', 'compensating', 'failed') NOT NULL DEFAULT 'pending' COMMENT 'Saga 状态',
  fail_reason     TEXT COMMENT '失败原因（执行或补偿失败）',
  step_index_mode ENUM('manual', 'auto') NOT NULL DEFAULT 'manual' COMMENT '步骤索引分配模式（manual：模板分配，auto：自动递增）',
  step_templates  VARCHAR(1000)  DEFAULT NULL COMMENT '当使用 manual 模式时，保存步骤模板（服务名、动作名、顺序）',
  cur_step_index  INT NOT NULL DEFAULT 0 COMMENT '当使用 auto 模式时，当前自增的 step_index',
  compensation_window_sec INT NOT NULL DEFAULT 30 COMMENT '补偿窗口期（秒），commit/rollback后仍接受补偿上报的时间窗口',
  created_at      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at  DATETIME DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Saga 分布式事务主表';


CREATE TABLE IF NOT EXISTS saga_steps (
  step_id       CHAR(32)     NOT NULL PRIMARY KEY COMMENT '步骤ID',
  saga_id       CHAR(36)      NOT NULL DEFAULT '' COMMENT '关联 Saga 事务',
  action     VARCHAR(64)   NOT NULL DEFAULT '' COMMENT '步骤名称',
  step_index           INT NOT NULL COMMENT '步骤顺序',
  service_name         VARCHAR(100) NOT NULL COMMENT '服务名称（业务服务）',
  context_data         JSON DEFAULT NULL COMMENT '正向执行的上下文参数（由服务自己使用）',
  compensation_context JSON DEFAULT NULL COMMENT '补偿需要的上下文参数（由 orchestrator 使用）',
  compensate_endpoint  VARCHAR(255) NOT NULL COMMENT '补偿接口 URL（如 http://order-svc/saga/compensate/CancelOrder）',
  compensation_status  ENUM('uninitialized', 'pending', 'running', 'completed', 'failed', 'delay') NOT NULL  DEFAULT 'uninitialized' COMMENT '补偿执行状态',
  last_error           TEXT COMMENT '失败信息或补偿失败信息',
  retry_count          INT DEFAULT 0 COMMENT '补偿重试次数',
  created_at           DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at           DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at  DATETIME DEFAULT NULL,
  KEY saga_step (saga_id, step_index),
  KEY step_id (step_id),
  UNIQUE KEY uq_saga_action_service (saga_id, action, service_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Saga 子事务步骤表';