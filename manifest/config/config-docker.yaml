# Saga 分布式事务服务配置 (Docker 环境)
saga:
  # 补偿恢复服务配置
  compensation:
    recovery:
      enabled: true               # Docker 环境启用补偿恢复服务
      taskTimeout: "5m"           # 任务超时时间
      recoveryInterval: "30s"     # 检查间隔
      maxConcurrentTasks: 5       # Docker 环境适中的并发数

    # 回滚配置
    rollback:
      maxRetries: 3               # 重试次数
      timeout: "30s"              # 超时时间

# 数据库配置 (使用 Docker 容器名)
database:
  default:
    link: "mysql:root:12345678a@tcp(mysql:3306)/saga"
    debug: false                  # 生产环境关闭调试模式
    maxIdle: 5                    # 最大空闲连接数
    maxOpen: 20                   # 最大打开连接数
    maxLifetime: "1h"             # 连接最大生存时间
    
# 服务器配置
server:
  address: ":8080"
  readTimeout: "30s"              # 读取超时
  writeTimeout: "30s"             # 写入超时
  idleTimeout: "60s"              # 空闲超时
  maxHeaderBytes: 1048576         # 最大请求头大小 (1MB)
  
# 日志配置
logger:
  level: "info"                   # Docker 环境使用 info 级别
  stdout: true
