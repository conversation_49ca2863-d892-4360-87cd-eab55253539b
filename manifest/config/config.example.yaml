# Saga 分布式事务服务配置 (开发环境示例)
saga:
  # 补偿恢复服务配置
  compensation:
    recovery:
      enabled: false          # 开发环境可选择禁用补偿恢复服务
      taskTimeout: "2m"       # 开发环境设置较短的超时时间
      recoveryInterval: "10s" # 开发环境设置较短的检查间隔
      maxConcurrentTasks: 5   # 开发环境设置较少的并发数

    # 回滚配置
    rollback:
      maxRetries: 2           # 开发环境设置较少的重试次数
      timeout: "15s"          # 开发环境设置较短的超时时间

# 数据库配置
database:
  default:
    link: "mysql:root:password@tcp(127.0.0.1:3306)/saga_dev"
    debug: true
    
# 服务器配置
server:
  address: ":8080"
  
# 日志配置
logger:
  level: "all"
  stdout: true 