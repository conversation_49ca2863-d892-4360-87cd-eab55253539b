# MySQL 性能测试优化配置 - 修复版
[mysqld]
# 基础配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接配置
max_connections = 500
max_connect_errors = 100000
max_user_connections = 450

# InnoDB 配置 (修复版)
innodb_buffer_pool_size = 4G  # 减少到8G，避免内存不足
innodb_buffer_pool_instances = 8
innodb_redo_log_capacity = 2G  # 正确的参数名
innodb_log_buffer_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_open_files = 4000
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000
innodb_read_io_threads = 8
innodb_write_io_threads = 8
innodb_thread_concurrency = 16
innodb_lock_wait_timeout = 120
innodb_rollback_on_timeout = 1

# 临时表配置
tmp_table_size = 256M
max_heap_table_size = 256M

# 排序和连接配置
sort_buffer_size = 8M
join_buffer_size = 8M
read_buffer_size = 2M
read_rnd_buffer_size = 8M

# 线程配置
thread_cache_size = 100
thread_stack = 256K

# 表配置
table_open_cache = 4000
table_definition_cache = 2000

# 二进制日志配置
log_bin = mysql-bin
binlog_format = ROW
binlog_cache_size = 2M
max_binlog_cache_size = 1G
max_binlog_size = 1G
binlog_expire_logs_seconds = 604800  # 替代 expire_logs_days
sync_binlog = 0

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/lib/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# 错误日志
log_error = /var/lib/mysql/error.log

# 性能模式
performance_schema = ON
performance_schema_max_table_instances = 12500
performance_schema_max_table_handles = 4000

# 其他优化
skip_name_resolve = 1
back_log = 500
wait_timeout = 28800
interactive_timeout = 28800
net_read_timeout = 30
net_write_timeout = 60
key_buffer_size = 256M

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4