# MySQL 轻量级配置
# 适用于开发环境和轻量级测试

[mysqld]
# 基础配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接配置
max_connections = 200
max_connect_errors = 10000
max_user_connections = 180

# InnoDB 配置 (MySQL 9.x 兼容)
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 2
# innodb_log_file_size 在 MySQL 8.0.30+ 中已废弃，使用 innodb_redo_log_capacity
innodb_redo_log_capacity = 512M
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 1
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_open_files = 1000
innodb_io_capacity = 200
innodb_io_capacity_max = 400
innodb_read_io_threads = 4
innodb_write_io_threads = 4
innodb_thread_concurrency = 8
innodb_lock_wait_timeout = 50

# 临时表配置
tmp_table_size = 64M
max_heap_table_size = 64M

# 排序和连接配置
sort_buffer_size = 2M
join_buffer_size = 2M
read_buffer_size = 1M
read_rnd_buffer_size = 2M

# 线程配置
thread_cache_size = 50
thread_stack = 192K

# 表配置
table_open_cache = 1000
table_definition_cache = 500

# 二进制日志配置
log_bin = mysql-bin
binlog_format = ROW
binlog_cache_size = 1M
max_binlog_size = 512M
expire_logs_days = 3
sync_binlog = 1

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/lib/mysql/slow.log
long_query_time = 5

# 错误日志
log_error = /var/lib/mysql/error.log

# 性能模式（轻量级）
performance_schema = ON
performance_schema_max_table_instances = 1000
performance_schema_max_table_handles = 500

# 其他配置
skip_name_resolve = 1
back_log = 100
wait_timeout = 28800
interactive_timeout = 28800
key_buffer_size = 64M

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
