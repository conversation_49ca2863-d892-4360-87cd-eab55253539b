package dao

import (
	"context"
	"os"
	"strings"
	"testing"
	"time"

	"saga/internal/consts"
	"saga/internal/model/entity"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/test/gtest"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/guid"
)

func TestMain(m *testing.M) {
	g.Log().SetDebug(true)

	// 运行测试
	code := m.Run()

	// 清理资源
	g.DB().Close(context.Background())
	os.Exit(code)
}

// TestCreateOrUpdateStep_SaveMethodOptimization 测试使用Save方法优化的CreateOrUpdateStep
func TestCreateOrUpdateStep_SaveMethodOptimization(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 创建测试数据 - 使用唯一的ID
		sagaId := guid.S()
		saga := &entity.SagaTransactions{
			SagaId:        sagaId,
			SagaStatus:    consts.SagaStatusRunning,
			Name:          "test-saga",
			StepIndexMode: "auto",
			StepTemplates: "[]",
			CurStepIndex:  0,
			CreatedAt:     gtime.Now(),
			UpdatedAt:     gtime.Now(),
		}

		// 插入测试事务 - 使用字符串主键
		result, err := SagaTransactions.Insert(ctx, saga)
		t.AssertNil(err)
		rowsAffected, err := result.RowsAffected()
		t.AssertNil(err)
		t.AssertGT(rowsAffected, 0)

		// 开始事务
		tx, err := SagaSteps.DB().Begin(ctx)
		t.AssertNil(err)
		defer tx.Rollback()

		// 测试步骤数据 - 使用字符串sagaId
		stepData := &entity.SagaSteps{
			StepId:              "step-save-001",
			SagaId:              sagaId,
			Action:              "create_order",
			StepIndex:           1,
			ServiceName:         "order-service",
			ContextData:         `{"orderId": "order-001"}`,
			CompensationContext: `{"orderId": "order-001"}`,
			CompensateEndpoint:  "/order/compensate",
			CompensationStatus:  consts.CompensationStatusUninitialized,
			LastError:           "",
			RetryCount:          0,
			CreatedAt:           gtime.Now(),
			UpdatedAt:           gtime.Now(),
		}

		// 第一次调用 - 应该创建新记录
		err = SagaSteps.CreateOrUpdateStep(ctx, tx, stepData)
		t.AssertNil(err)

		// 查询创建的记录
		var step1 entity.SagaSteps
		err = SagaSteps.DB().Model(SagaSteps.Table()).Ctx(ctx).TX(tx).
			Where("saga_id = ? AND action = ? AND service_name = ?", sagaId, "create_order", "order-service").
			Scan(&step1)
		t.AssertNil(err)
		t.AssertEQ(step1.Action, "create_order")
		t.AssertEQ(step1.ServiceName, "order-service")
		t.AssertEQ(step1.StepIndex, 1)
		t.AssertEQ(step1.CompensationStatus, consts.CompensationStatusUninitialized)

		// 修改步骤数据
		stepData.CompensationStatus = consts.CompensationStatusCompleted
		stepData.ContextData = `{"orderId": "order-001", "status": "processed"}`
		stepData.StepIndex = 2 // 修改step_index

		// 第二次调用 - 应该更新现有记录
		err = SagaSteps.CreateOrUpdateStep(ctx, tx, stepData)
		t.AssertNil(err)

		// 查询更新的记录
		var step2 entity.SagaSteps
		err = SagaSteps.DB().Model(SagaSteps.Table()).Ctx(ctx).TX(tx).
			Where("saga_id = ? AND action = ? AND service_name = ?", sagaId, "create_order", "order-service").
			Scan(&step2)
		t.AssertNil(err)
		t.AssertEQ(step2.Action, "create_order")
		t.AssertEQ(step2.ServiceName, "order-service")
		t.AssertEQ(step2.StepIndex, 1) // 验证step_index 不会改变
		t.AssertEQ(step2.CompensationStatus, consts.CompensationStatusCompleted)

		// 验证第二次调用的ContextData包含预期的JSON内容
		t.AssertNE(step2.ContextData, "")
		t.AssertEQ(strings.Contains(step2.ContextData, `"orderId": "order-001"`), true)
		t.AssertEQ(strings.Contains(step2.ContextData, `"status": "processed"`), true)

		// 验证记录数量 - 应该只有一条记录
		count, err := SagaSteps.DB().Model(SagaSteps.Table()).
			Ctx(ctx).TX(tx).
			Where("saga_id = ? AND action = ? AND service_name = ?",
				sagaId, "create_order", "order-service").
			Count()
		t.AssertNil(err)
		t.AssertEQ(count, 1)
	})
}

// TestCreateOrUpdateStep_ConcurrentSaveOptimization 测试Save方法在并发环境下的优化效果
func TestCreateOrUpdateStep_ConcurrentSaveOptimization(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 创建测试数据 - 使用唯一的ID
		sagaId := guid.S()
		saga := &entity.SagaTransactions{
			SagaId:        sagaId,
			SagaStatus:    consts.SagaStatusRunning,
			Name:          "test-saga",
			StepIndexMode: "auto",
			StepTemplates: "[]",
			CurStepIndex:  0,
			CreatedAt:     gtime.Now(),
			UpdatedAt:     gtime.Now(),
		}

		// 插入测试事务
		result, err := SagaTransactions.Insert(ctx, saga)
		t.AssertNil(err)
		rowsAffected, err := result.RowsAffected()
		t.AssertNil(err)
		t.AssertGT(rowsAffected, 0)

		// 并发测试：多个goroutine同时调用CreateOrUpdateStep
		const goroutineCount = 10
		results := make(chan *entity.SagaSteps, goroutineCount)
		errors := make(chan error, goroutineCount)

		// 启动多个goroutine同时操作
		for i := 0; i < goroutineCount; i++ {
			go func(index int) {
				// 每个goroutine使用自己的事务
				tx, err := SagaSteps.DB().Begin(ctx)
				if err != nil {
					errors <- err
					return
				}
				defer tx.Rollback()

				stepData := &entity.SagaSteps{
					StepId:              "step-concurrent-save-001",
					SagaId:              sagaId,
					Action:              "concurrent_test",
					StepIndex:           index + 1, // 每个goroutine设置不同的step_index
					ServiceName:         "concurrent-service",
					ContextData:         `{"index": ` + gconv.String(index) + `}`,
					CompensationContext: `{"index": ` + gconv.String(index) + `}`,
					CompensateEndpoint:  "/concurrent/compensate",
					CompensationStatus:  consts.CompensationStatusUninitialized,
					LastError:           "",
					RetryCount:          0,
					CreatedAt:           gtime.Now(),
					UpdatedAt:           gtime.Now(),
				}

				err = SagaSteps.CreateOrUpdateStep(ctx, tx, stepData)
				if err != nil {
					errors <- err
					return
				}

				// 提交事务
				err = tx.Commit()
				if err != nil {
					errors <- err
					return
				}

				results <- stepData
			}(i)
		}

		// 收集结果
		var successCount int
		var errorCount int

		for i := 0; i < goroutineCount; i++ {
			select {
			case result := <-results:
				if result != nil {
					successCount++
				}
			case err := <-errors:
				if err != nil {
					errorCount++
				}
			case <-time.After(time.Second * 5):
				t.Fatal("测试超时")
			}
		}

		// 验证结果
		// 由于使用了Save方法和唯一约束，所有操作都应该成功
		// 最终应该只有一条记录（最后一个goroutine的数据）
		t.Log("成功操作数：", successCount)
		t.Log("错误操作数：", errorCount)

		// 验证最终数据库状态
		count, err := SagaSteps.DB().Model(SagaSteps.Table()).
			Ctx(ctx).
			Where("saga_id = ? AND action = ? AND service_name = ?",
				sagaId, "concurrent_test", "concurrent-service").
			Count()
		t.AssertNil(err)
		t.AssertEQ(count, 1) // 应该只有一条记录

		// 验证记录内容
		var resultStep entity.SagaSteps
		err = SagaSteps.DB().Model(SagaSteps.Table()).
			Ctx(ctx).
			Where("saga_id = ? AND action = ? AND service_name = ?",
				sagaId, "concurrent_test", "concurrent-service").
			Scan(&resultStep)
		t.AssertNil(err)
		t.AssertEQ(resultStep.Action, "concurrent_test")
		t.AssertEQ(resultStep.ServiceName, "concurrent-service")
		t.AssertGT(resultStep.StepIndex, 0) // step_index应该是某个goroutine设置的值
	})
}

// TestCreateOrUpdateStep_SaveMethodPerformance 测试Save方法的性能
func TestCreateOrUpdateStep_SaveMethodPerformance(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 创建测试数据 - 使用唯一的ID
		sagaId := guid.S()
		saga := &entity.SagaTransactions{
			SagaId:        sagaId,
			SagaStatus:    consts.SagaStatusRunning,
			Name:          "test-saga",
			StepIndexMode: "auto",
			StepTemplates: "[]",
			CurStepIndex:  0,
			CreatedAt:     gtime.Now(),
			UpdatedAt:     gtime.Now(),
		}

		// 插入测试事务
		result, err := SagaTransactions.Insert(ctx, saga)
		t.AssertNil(err)
		rowsAffected, err := result.RowsAffected()
		t.AssertNil(err)
		t.AssertGT(rowsAffected, 0)

		// 性能测试：连续调用多次
		const iterations = 100
		start := time.Now()

		for i := 0; i < iterations; i++ {
			tx, err := SagaSteps.DB().Begin(ctx)
			t.AssertNil(err)

			stepData := &entity.SagaSteps{
				StepId:              "step-performance-save-001",
				SagaId:              sagaId,
				Action:              "performance_test",
				StepIndex:           i + 1,
				ServiceName:         "performance-service",
				ContextData:         `{"iteration": ` + gconv.String(i) + `}`,
				CompensationContext: `{"iteration": ` + gconv.String(i) + `}`,
				CompensateEndpoint:  "/performance/compensate",
				CompensationStatus:  consts.CompensationStatusUninitialized,
				LastError:           "",
				RetryCount:          0,
				CreatedAt:           gtime.Now(),
				UpdatedAt:           gtime.Now(),
			}

			err = SagaSteps.CreateOrUpdateStep(ctx, tx, stepData)
			t.AssertNil(err)

			err = tx.Commit()
			t.AssertNil(err)
		}

		duration := time.Since(start)
		t.Log("Save方法性能测试完成，耗时：", duration)
		t.Log("每次操作平均耗时：", duration/iterations)

		// 验证最终只有一条记录
		count, err := SagaSteps.DB().Model(SagaSteps.Table()).
			Ctx(ctx).
			Where("saga_id = ? AND action = ? AND service_name = ?",
				sagaId, "performance_test", "performance-service").
			Count()
		t.AssertNil(err)
		t.AssertEQ(count, 1)

		// 验证最终记录是最后一次迭代的数据
		var resultStep entity.SagaSteps
		err = SagaSteps.DB().Model(SagaSteps.Table()).
			Ctx(ctx).
			Where("saga_id = ? AND action = ? AND service_name = ?",
				sagaId, "performance_test", "performance-service").
			Scan(&resultStep)
		t.AssertNil(err)
		t.AssertEQ(resultStep.StepIndex, 1)
		t.AssertEQ(strings.Contains(resultStep.ContextData, `"iteration": `+gconv.String(iterations-1)+`}`), true)
	})
}
