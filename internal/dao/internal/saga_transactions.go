// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SagaTransactionsDao is the data access object for the table saga_transactions.
type SagaTransactionsDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  SagaTransactionsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// SagaTransactionsColumns defines and stores column names for the table saga_transactions.
type SagaTransactionsColumns struct {
	SagaId                string // Saga 事务唯一 ID（UUID）
	Name                  string // Saga 名称
	SagaStatus            string // Saga 状态
	FailReason            string // 失败原因（执行或补偿失败）
	StepIndexMode         string // 步骤索引分配模式（manual：模板分配，auto：自动递增）
	StepTemplates         string // 当使用 manual 模式时，保存步骤模板（服务名、动作名、顺序）
	CurStepIndex          string // 当使用 auto 模式时，当前自增的 step_index
	CompensationWindowSec string // 补偿窗口期（秒），commit/rollback后仍接受补偿上报的时间窗口
	CreatedAt             string //
	UpdatedAt             string //
	DeletedAt             string //
}

// sagaTransactionsColumns holds the columns for the table saga_transactions.
var sagaTransactionsColumns = SagaTransactionsColumns{
	SagaId:                "saga_id",
	Name:                  "name",
	SagaStatus:            "saga_status",
	FailReason:            "fail_reason",
	StepIndexMode:         "step_index_mode",
	StepTemplates:         "step_templates",
	CurStepIndex:          "cur_step_index",
	CompensationWindowSec: "compensation_window_sec",
	CreatedAt:             "created_at",
	UpdatedAt:             "updated_at",
	DeletedAt:             "deleted_at",
}

// NewSagaTransactionsDao creates and returns a new DAO object for table data access.
func NewSagaTransactionsDao(handlers ...gdb.ModelHandler) *SagaTransactionsDao {
	return &SagaTransactionsDao{
		group:    "default",
		table:    "saga_transactions",
		columns:  sagaTransactionsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SagaTransactionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SagaTransactionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SagaTransactionsDao) Columns() SagaTransactionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SagaTransactionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SagaTransactionsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SagaTransactionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
