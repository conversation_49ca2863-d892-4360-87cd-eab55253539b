// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SagaStepsDao is the data access object for the table saga_steps.
type SagaStepsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SagaStepsColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SagaStepsColumns defines and stores column names for the table saga_steps.
type SagaStepsColumns struct {
	StepId              string // 步骤ID
	SagaId              string // 关联 Saga 事务
	Action              string // 步骤名称
	StepIndex           string // 步骤顺序
	ServiceName         string // 服务名称（业务服务）
	ContextData         string // 正向执行的上下文参数（由服务自己使用）
	CompensationContext string // 补偿需要的上下文参数（由 orchestrator 使用）
	CompensateEndpoint  string // 补偿接口 URL（如 http://order-svc/saga/compensate/CancelOrder）
	CompensationStatus  string // 补偿执行状态
	LastError           string // 失败信息或补偿失败信息
	RetryCount          string // 补偿重试次数
	CreatedAt           string //
	UpdatedAt           string //
	DeletedAt           string //
}

// sagaStepsColumns holds the columns for the table saga_steps.
var sagaStepsColumns = SagaStepsColumns{
	StepId:              "step_id",
	SagaId:              "saga_id",
	Action:              "action",
	StepIndex:           "step_index",
	ServiceName:         "service_name",
	ContextData:         "context_data",
	CompensationContext: "compensation_context",
	CompensateEndpoint:  "compensate_endpoint",
	CompensationStatus:  "compensation_status",
	LastError:           "last_error",
	RetryCount:          "retry_count",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	DeletedAt:           "deleted_at",
}

// NewSagaStepsDao creates and returns a new DAO object for table data access.
func NewSagaStepsDao(handlers ...gdb.ModelHandler) *SagaStepsDao {
	return &SagaStepsDao{
		group:    "default",
		table:    "saga_steps",
		columns:  sagaStepsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SagaStepsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SagaStepsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SagaStepsDao) Columns() SagaStepsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SagaStepsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SagaStepsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SagaStepsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
