// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"saga/internal/consts"
	"saga/internal/dao/internal"
	"saga/internal/model"
	"saga/internal/model/do"
	"saga/internal/model/entity"
	"time"

	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
)

// sagaStepsDao is the data access object for the table saga_steps.
// You can define custom methods on it to extend its functionality as needed.
type sagaStepsDao struct {
	*internal.SagaStepsDao
}

var (
	// SagaSteps is a globally accessible object for table saga_steps operations.
	SagaSteps = sagaStepsDao{internal.NewSagaStepsDao()}
)

// Add your custom methods and functionality below.

// FindBySagaId 根据 SagaId 查找所有步骤记录
func (dao *sagaStepsDao) FindBySagaId(ctx context.Context, sagaId string) ([]*entity.SagaSteps, error) {
	var steps []*entity.SagaSteps
	err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		OrderAsc(dao.Columns().StepIndex).
		Scan(&steps)

	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	}

	if err != nil {
		return nil, fmt.Errorf("查询步骤记录失败: %w", err)
	}

	return steps, nil
}

// FindByStepId 根据 StepId 查找步骤记录
func (dao *sagaStepsDao) FindByStepId(ctx context.Context, stepId string) (*entity.SagaSteps, error) {
	var step *entity.SagaSteps
	err := dao.Ctx(ctx).
		Where(dao.Columns().StepId, stepId).
		Scan(&step)

	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	}

	if err != nil {
		return nil, fmt.Errorf("查询步骤记录失败: %w", err)
	}

	return step, nil
}

// FindByActionAndService 根据 SagaId、Action 和 ServiceName 查找步骤记录（用于幂等性检查）
func (dao *sagaStepsDao) FindByActionAndService(ctx context.Context, sagaId, action, serviceName string) (*entity.SagaSteps, error) {
	var step *entity.SagaSteps
	err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().Action, action).
		Where(dao.Columns().ServiceName, serviceName).
		Scan(&step)

	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	}

	if err != nil {
		return nil, fmt.Errorf("查询步骤记录失败: %w", err)
	}

	return step, nil
}

// FindByActionAndServiceWithTx 根据 SagaId、Action 和 ServiceName 查找步骤记录（事务版本）
func (dao *sagaStepsDao) FindByActionAndServiceWithTx(ctx context.Context, tx gdb.TX, sagaId, action, serviceName string) (*entity.SagaSteps, error) {
	var step *entity.SagaSteps
	err := dao.Ctx(ctx).TX(tx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().Action, action).
		Where(dao.Columns().ServiceName, serviceName).
		Scan(&step)

	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	}

	if err != nil {
		return nil, fmt.Errorf("查询步骤记录失败: %w", err)
	}

	return step, nil
}

// CreateOrUpdateStep 创建或更新步骤
//
// 重要特性：step_index 一旦创建就不能更改
//
// 工作原理：
// 1. 对于新记录：直接插入，包括 step_index
// 2. 对于已存在的记录（基于 saga_id + action + service_name 唯一约束）：
//   - 更新除 step_index 之外的所有字段
//   - step_index 字段被 OnDuplicateEx 排除，保持原值不变
//
// 这种设计确保了：
// - 步骤索引的稳定性：一旦分配就不会改变
// - 补偿操作的可靠性：回滚时能按正确的逆序执行
// - 数据一致性：避免因索引变化导致的业务逻辑错误
//
// 参数：
//   - ctx: 上下文
//   - tx: 数据库事务
//   - step: 要保存的步骤实体
//
// 返回：
//   - error: 保存失败时返回错误
func (dao *sagaStepsDao) CreateOrUpdateStep(ctx context.Context, tx gdb.TX, step *entity.SagaSteps) error {

	// 设置时间戳
	now := gtime.Now()
	if step.CreatedAt == nil {
		step.CreatedAt = now
	}
	step.UpdatedAt = now

	// 使用 Save 方法进行保存
	// OnDuplicateEx(dao.Columns().StepIndex) 确保在遇到唯一约束冲突时，
	// step_index 字段不会被更新，从而保证步骤索引的不可变性
	result, err := dao.DB().Model(dao.Table()).Ctx(ctx).
		OnDuplicateEx(dao.Columns().StepIndex).TX(tx).Save(step)
	if err != nil {
		return fmt.Errorf("保存步骤失败: %w", err)
	}

	// 检查操作结果
	affectedRows, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}
	if affectedRows == 0 {
		return fmt.Errorf("没有记录被保存")
	}

	return nil
}

// UpdateExistingStepWithTx 更新已存在的步骤
func (dao *sagaStepsDao) UpdateExistingStepWithTx(ctx context.Context, tx gdb.TX, stepId string, update *do.SagaSteps) error {
	updateData := g.Map{}

	if update.CompensationContext != nil {
		updateData[dao.Columns().CompensationContext] = update.CompensationContext
	}
	if update.CompensationStatus != nil {
		updateData[dao.Columns().CompensationStatus] = update.CompensationStatus
	}
	if update.LastError != nil {
		updateData[dao.Columns().LastError] = update.LastError
	}
	if update.RetryCount != nil {
		updateData[dao.Columns().RetryCount] = update.RetryCount
	}

	// 总是更新 updated_at
	updateData[dao.Columns().UpdatedAt] = gtime.Now()

	_, err := dao.DB().Model(dao.Table()).Ctx(ctx).TX(tx).Where(dao.Columns().StepId, stepId).Update(updateData)
	return err
}

// CountBySagaId 统计指定 SagaId 的步骤数量
func (dao *sagaStepsDao) CountBySagaId(ctx context.Context, sagaId string) (int, error) {
	count, err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Count()

	if err != nil {
		return 0, fmt.Errorf("统计步骤记录失败: %w", err)
	}

	return count, nil
}

// GetMaxStepIndexBySagaId 获取指定 SagaId 的最大步骤索引
func (dao *sagaStepsDao) GetMaxStepIndexBySagaId(ctx context.Context, sagaId string) (int, error) {
	var maxStepIndex int
	err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		OrderDesc(dao.Columns().StepIndex).
		Limit(1).
		Fields(dao.Columns().StepIndex).
		Scan(&maxStepIndex)

	if err != nil {
		return 0, fmt.Errorf("获取最大步骤索引失败: %w", err)
	}

	return maxStepIndex, nil
}

// CheckStepExistsBySagaIdAndTemplate 检查指定模板的步骤是否存在
func (dao *sagaStepsDao) CheckStepExistsBySagaIdAndTemplate(ctx context.Context, sagaId string, serviceName string, action string) (bool, error) {
	count, err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().ServiceName, serviceName).
		Where(dao.Columns().Action, action).
		Count()

	if err != nil {
		return false, fmt.Errorf("检查步骤是否存在失败: %w", err)
	}

	return count > 0, nil
}

// FindStepsForRollback 查找需要回滚的步骤（按step_index逆序）
// compensationStatus: 指定要查询的补偿状态，例如"uninitialized"表示已成功完成的步骤
func (dao *sagaStepsDao) FindStepsForRollback(ctx context.Context, sagaId string, compensationStatus string) ([]*entity.SagaSteps, error) {
	var steps []*entity.SagaSteps
	err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().CompensationStatus, compensationStatus). // 查询指定补偿状态的步骤
		OrderDesc(dao.Columns().StepIndex).                          // 按步骤索引逆序排列
		Scan(&steps)

	if err != nil {
		return nil, fmt.Errorf("查询回滚步骤记录失败: %w", err)
	}

	return steps, nil
}

// UpdateCompensationStatus 更新步骤的补偿状态
func (dao *sagaStepsDao) UpdateCompensationStatus(ctx context.Context, stepId string, status string, lastError string, retryCount int) error {
	updateData := map[string]interface{}{
		dao.Columns().CompensationStatus: status,
		dao.Columns().LastError:          lastError,
		dao.Columns().RetryCount:         retryCount,
		dao.Columns().UpdatedAt:          gtime.Now(),
	}

	_, err := dao.Ctx(ctx).
		Where(dao.Columns().StepId, stepId).
		Update(updateData)

	if err != nil {
		return fmt.Errorf("更新步骤补偿状态失败: %w", err)
	}

	return nil
}

// UpdateCompensationStatusWithTx 在事务中更新步骤的补偿状态
func (dao *sagaStepsDao) UpdateCompensationStatusWithTx(ctx context.Context, tx gdb.TX, sagaId string, stepIndex int, status string, lastError string, retryCount int) error {
	updateData := map[string]interface{}{
		dao.Columns().CompensationStatus: status,
		dao.Columns().LastError:          lastError,
		dao.Columns().RetryCount:         retryCount,
		dao.Columns().UpdatedAt:          gtime.Now(),
	}

	_, err := dao.Ctx(ctx).TX(tx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().StepIndex, stepIndex).
		Update(updateData)

	if err != nil {
		return fmt.Errorf("更新步骤补偿状态失败: %w", err)
	}

	return nil
}

// FindStepByIndex 根据SagaId和StepIndex查找步骤记录
func (dao *sagaStepsDao) FindStepByIndex(ctx context.Context, sagaId string, stepIndex int) (*entity.SagaSteps, error) {
	var step *entity.SagaSteps
	err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().StepIndex, stepIndex).
		Scan(&step)

	if err != nil {
		return nil, fmt.Errorf("查询步骤记录失败: %w", err)
	}

	if step == nil {
		return nil, fmt.Errorf("步骤记录不存在: sagaId=%s, stepIndex=%d", sagaId, stepIndex)
	}

	return step, nil
}

// FindStepById 根据StepId查找步骤记录
func (dao *sagaStepsDao) FindStepById(ctx context.Context, stepId string) (*entity.SagaSteps, error) {
	var step *entity.SagaSteps
	err := dao.Ctx(ctx).
		Where(dao.Columns().StepId, stepId).
		Scan(&step)

	if err != nil {
		return nil, fmt.Errorf("查询步骤记录失败: %w", err)
	}

	if step == nil {
		return nil, fmt.Errorf("步骤记录不存在: stepId=%s", stepId)
	}

	return step, nil
}

// CountCompensationsByStatus 统计指定补偿状态的步骤数量
func (dao *sagaStepsDao) CountCompensationsByStatus(ctx context.Context, sagaId string, status string) (int, error) {
	count, err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().CompensationStatus, status).
		Count()

	if err != nil {
		return 0, fmt.Errorf("统计补偿状态步骤数量失败: %w", err)
	}

	return count, nil
}

// GetCompensationStatistics 获取补偿统计信息
func (dao *sagaStepsDao) GetCompensationStatistics(ctx context.Context, sagaId string) (*model.CompensationStatistics, error) {
	stats := &model.CompensationStatistics{}
	var err error

	// 统计总数
	stats.Total, err = dao.CountBySagaId(ctx, sagaId)
	if err != nil {
		return nil, fmt.Errorf("统计总步骤数失败: %w", err)
	}

	// 统计已完成补偿
	stats.Completed, err = dao.CountCompensationsByStatus(ctx, sagaId, consts.CompensationStatusCompleted)
	if err != nil {
		return nil, fmt.Errorf("统计已完成补偿数失败: %w", err)
	}

	// 统计失败补偿
	stats.Failed, err = dao.CountCompensationsByStatus(ctx, sagaId, consts.CompensationStatusFailed)
	if err != nil {
		return nil, fmt.Errorf("统计失败补偿数失败: %w", err)
	}

	// 统计待处理补偿
	stats.Uninitialized, err = dao.CountCompensationsByStatus(ctx, sagaId, consts.CompensationStatusUninitialized)
	if err != nil {
		return nil, fmt.Errorf("统计待处理补偿数失败: %w", err)
	}

	// 统计待处理补偿
	stats.Pending, err = dao.CountCompensationsByStatus(ctx, sagaId, consts.CompensationStatusPending)
	if err != nil {
		return nil, fmt.Errorf("统计待处理补偿数失败: %w", err)
	}

	// 统计运行中补偿
	stats.Running, err = dao.CountCompensationsByStatus(ctx, sagaId, consts.CompensationStatusRunning)
	if err != nil {
		return nil, fmt.Errorf("统计运行中补偿数失败: %w", err)
	}

	// 统计延期上报补偿
	stats.Delay, err = dao.CountCompensationsByStatus(ctx, sagaId, consts.CompensationStatusDelay)
	if err != nil {
		return nil, fmt.Errorf("统计延期上报补偿数失败: %w", err)
	}

	return stats, nil
}

// BatchUpdateCompensationStatusWithTx 批量更新指定条件下的步骤补偿状态（事务版本）
// 用于批量将uninitialized状态的步骤更新为pending状态，提高性能
func (dao *sagaStepsDao) BatchUpdateCompensationStatusWithTx(ctx context.Context, tx gdb.TX, sagaId string,
	fromStatus string, toStatus string, lastError string, retryCount int) (int64, error) {
	updateData := map[string]interface{}{
		dao.Columns().CompensationStatus: toStatus,
		dao.Columns().LastError:          lastError,
		dao.Columns().RetryCount:         retryCount,
		dao.Columns().UpdatedAt:          gtime.Now(),
	}

	result, err := dao.Ctx(ctx).TX(tx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().CompensationStatus, fromStatus).
		Update(updateData)

	if err != nil {
		return 0, fmt.Errorf("批量更新步骤补偿状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取批量更新影响行数失败: %w", err)
	}

	return rowsAffected, nil
}

// BatchUpdateCompensationStatus 批量更新指定条件下的步骤补偿状态（非事务版本）
func (dao *sagaStepsDao) BatchUpdateCompensationStatus(ctx context.Context, sagaId string, fromStatus string, toStatus string, lastError string, retryCount int) (int64, error) {
	updateData := map[string]interface{}{
		dao.Columns().CompensationStatus: toStatus,
		dao.Columns().LastError:          lastError,
		dao.Columns().RetryCount:         retryCount,
		dao.Columns().UpdatedAt:          gtime.Now(),
	}

	result, err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Where(dao.Columns().CompensationStatus, fromStatus).
		Update(updateData)

	if err != nil {
		return 0, fmt.Errorf("批量更新步骤补偿状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取批量更新影响行数失败: %w", err)
	}

	return rowsAffected, nil
}

// FindUnfinishedCompensations 查找所有未完成的补偿任务
// 返回状态为 pending 或 running 的补偿任务，用于服务重启后的恢复
func (dao *sagaStepsDao) FindUnfinishedCompensations(ctx context.Context) ([]*entity.SagaSteps, error) {
	var steps []*entity.SagaSteps
	err := dao.Ctx(ctx).
		WhereLTE(dao.Columns().RetryCount, consts.DefaultMaxRetries).
		WhereIn(dao.Columns().CompensationStatus, []string{consts.CompensationStatusPending,
							consts.CompensationStatusRunning, consts.CompensationStatusDelay}).
		OrderAsc(dao.Columns().CreatedAt). // 按创建时间排序，优先处理早期的任务
		Scan(&steps)

	if err != nil {
		return nil, fmt.Errorf("查询未完成补偿任务失败: %w", err)
	}

	return steps, nil
}

// FindUnfinishedCompensationsBySagaId 查找指定 SagaId 的未完成补偿任务
func (dao *sagaStepsDao) FindUnfinishedCompensationsBySagaId(ctx context.Context, sagaId string) ([]*entity.SagaSteps, error) {
	var steps []*entity.SagaSteps
	err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		WhereIn(dao.Columns().CompensationStatus, []string{consts.CompensationStatusPending,
							consts.CompensationStatusRunning, consts.CompensationStatusDelay}).
		OrderDesc(dao.Columns().StepIndex). // 按步骤索引逆序排列
		Scan(&steps)

	if err != nil {
		return nil, fmt.Errorf("查询指定Saga的未完成补偿任务失败: %w", err)
	}

	return steps, nil
}

// FindStaleRunningCompensations 查找超时的运行中补偿任务
// timeoutMinutes: 超时时间（分钟）
func (dao *sagaStepsDao) FindStaleRunningCompensations(ctx context.Context, timeoutMinutes int) ([]*entity.SagaSteps, error) {
	var steps []*entity.SagaSteps
	timeoutTime := gtime.Now().Add(-time.Duration(timeoutMinutes) * time.Minute)

	err := dao.Ctx(ctx).
		Where(dao.Columns().CompensationStatus, consts.CompensationStatusRunning).
		WhereLT(dao.Columns().UpdatedAt, timeoutTime).
		OrderAsc(dao.Columns().UpdatedAt). // 按更新时间排序，优先处理最早的任务
		Scan(&steps)

	if err != nil {
		return nil, fmt.Errorf("查询超时的运行中补偿任务失败: %w", err)
	}

	return steps, nil
}

// ResetStaleRunningCompensations 重置超时的运行中补偿任务为待处理状态
func (dao *sagaStepsDao) ResetStaleRunningCompensations(ctx context.Context, timeoutMinutes int) (int64, error) {
	timeoutTime := gtime.Now().Add(-time.Duration(timeoutMinutes) * time.Minute)

	updateData := map[string]interface{}{
		dao.Columns().CompensationStatus: consts.CompensationStatusPending,
		dao.Columns().LastError:          "任务超时，已重置为待处理状态",
		dao.Columns().UpdatedAt:          gtime.Now(),
	}

	result, err := dao.Ctx(ctx).
		Where(dao.Columns().CompensationStatus, consts.CompensationStatusRunning).
		WhereLT(dao.Columns().UpdatedAt, timeoutTime).
		Update(updateData)

	if err != nil {
		return 0, fmt.Errorf("重置超时补偿任务失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取重置任务影响行数失败: %w", err)
	}

	return rowsAffected, nil
}

// CountUnfinishedCompensations 统计未完成的补偿任务数量
func (dao *sagaStepsDao) CountUnfinishedCompensations(ctx context.Context) (total, pending, running, delay int, err error) {
	// 统计待处理的补偿任务
	pending, err = dao.CountCompensationsByStatusGlobal(ctx, consts.CompensationStatusPending)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("统计待处理补偿任务失败: %w", err)
	}

	// 统计运行中的补偿任务
	running, err = dao.CountCompensationsByStatusGlobal(ctx, consts.CompensationStatusRunning)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("统计运行中补偿任务失败: %w", err)
	}

	// 统计延期上报的补偿任务
	delay, err = dao.CountCompensationsByStatusGlobal(ctx, consts.CompensationStatusDelay)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("统计延期上报补偿任务失败: %w", err)
	}

	total = pending + running + delay

	return total, pending, running, delay, nil
}

// CountCompensationsByStatus 统计指定补偿状态的步骤数量（支持全局统计）
func (dao *sagaStepsDao) CountCompensationsByStatusGlobal(ctx context.Context, status string) (int, error) {
	query := dao.Ctx(ctx).Where(dao.Columns().CompensationStatus, status)

	count, err := query.Count()

	if err != nil {
		return 0, fmt.Errorf("统计补偿状态步骤数量失败: %w", err)
	}

	return count, nil
}
