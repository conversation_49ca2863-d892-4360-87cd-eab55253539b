// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"saga/internal/dao/internal"
	"saga/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
)

// sagaTransactionsDao is the data access object for the table saga_transactions.
// You can define custom methods on it to extend its functionality as needed.
type sagaTransactionsDao struct {
	*internal.SagaTransactionsDao
}

var (
	// SagaTransactions is a globally accessible object for table saga_transactions operations.
	SagaTransactions = sagaTransactionsDao{internal.NewSagaTransactionsDao()}
)

// Add your custom methods and functionality below.
func (dao *sagaTransactionsDao) Insert(ctx context.Context, data *entity.SagaTransactions) (sql.Result, error) {
	result, err := dao.Ctx(ctx).Insert(data)
	if err != nil {
		return nil, fmt.Errorf("插入Saga事务记录失败: %v", err)
	}
	return result, nil
}

func (dao *sagaTransactionsDao) UpdateWithTx(ctx context.Context, sagaId string, tx gdb.TX,
	updateData map[string]interface{}) error {
	_, err := dao.Ctx(ctx).TX(tx).
		Where(dao.Columns().SagaId, sagaId).
		Update(updateData)

	if err != nil {
		return fmt.Errorf("更新当前步骤索引失败: %w", err)
	}

	return nil
}

// FindBySagaId 根据SagaId查找Saga事务记录
func (dao *sagaTransactionsDao) FindBySagaId(ctx context.Context, sagaId string) (*entity.SagaTransactions, error) {
	var result *entity.SagaTransactions
	err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Scan(&result)

	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	}

	if err != nil {
		return nil, fmt.Errorf("查询Saga事务记录失败: %w", err)
	}

	return result, nil
}

// ExistsBySagaId 检查指定SagaId的记录是否存在
func (dao *sagaTransactionsDao) ExistsBySagaId(ctx context.Context, sagaId string) (bool, error) {
	count, err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Count()

	if err != nil {
		return false, fmt.Errorf("检查Saga事务记录是否存在失败: %w", err)
	}

	return count > 0, nil
}

// UpdateStatus 更新 Saga 事务状态
func (dao *sagaTransactionsDao) UpdateStatus(ctx context.Context, sagaId string, status string) error {
	_, err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Update(map[string]interface{}{
			dao.Columns().SagaStatus: status,
			dao.Columns().UpdatedAt:  gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("更新Saga事务状态失败: %w", err)
	}

	return nil
}

// UpdateStatusWithTx 在事务中更新 Saga 事务状态
func (dao *sagaTransactionsDao) UpdateStatusWithTx(ctx context.Context, sagaId string, status string, tx gdb.TX) error {
	_, err := dao.Ctx(ctx).TX(tx).
		Where(dao.Columns().SagaId, sagaId).
		Update(map[string]interface{}{
			dao.Columns().SagaStatus: status,
			dao.Columns().UpdatedAt:  gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("更新Saga事务状态失败: %w", err)
	}

	return nil
}

// UpdateFailReason 更新 Saga 事务失败原因
func (dao *sagaTransactionsDao) UpdateFailReason(ctx context.Context, sagaId string, failReason string) error {
	_, err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Update(map[string]interface{}{
			dao.Columns().FailReason: failReason,
			dao.Columns().UpdatedAt:  gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("更新Saga事务失败原因失败: %w", err)
	}

	return nil
}

// UpdateStatusAndFailReason 同时更新状态和失败原因
func (dao *sagaTransactionsDao) UpdateStatusAndFailReason(ctx context.Context, sagaId string, status string, failReason string) error {
	_, err := dao.Ctx(ctx).
		Where(dao.Columns().SagaId, sagaId).
		Update(map[string]interface{}{
			dao.Columns().SagaStatus: status,
			dao.Columns().FailReason: failReason,
			dao.Columns().UpdatedAt:  gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("更新Saga事务状态和失败原因失败: %w", err)
	}

	return nil
}

// UpdateStatusAndFailReasonWithTx 在事务中同时更新状态和失败原因
func (dao *sagaTransactionsDao) UpdateStatusAndFailReasonWithTx(ctx context.Context, sagaId string, status string, failReason string, tx gdb.TX) error {
	_, err := dao.Ctx(ctx).TX(tx).
		Where(dao.Columns().SagaId, sagaId).
		Update(map[string]interface{}{
			dao.Columns().SagaStatus: status,
			dao.Columns().FailReason: failReason,
			dao.Columns().UpdatedAt:  gtime.Now(),
		})

	if err != nil {
		return fmt.Errorf("更新Saga事务状态和失败原因失败: %w", err)
	}

	return nil
}

// LockAndGetSaga 锁定并获取 Saga 事务记录
func (dao *sagaTransactionsDao) LockAndGetSaga(ctx context.Context, tx gdb.TX, sagaId string) (*entity.SagaTransactions, error) {
	var sagaTransaction *entity.SagaTransactions
	err := dao.Ctx(ctx).TX(tx).Where("saga_id", sagaId).LockUpdate().Scan(&sagaTransaction)
	if err != nil {
		return nil, fmt.Errorf("查询事务失败: %w", err)
	}

	if sagaTransaction == nil {
		return nil, nil // 返回 nil 而不是错误，让上层处理
	}

	return sagaTransaction, nil
}
