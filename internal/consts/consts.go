package consts

import "time"

// Saga 状态枚举
const (
	SagaStatusPending      = "pending"      // 待处理
	SagaStatusRunning      = "running"      // 运行中
	SagaStatusCompleted    = "completed"    // 已完成
	SagaStatusCompensating = "compensating" // 补偿中
	SagaStatusFailed       = "failed"       // 已失败
)

// StepIndex 管理模式
const (
	StepIndexModeAuto   = "auto"   // 自动递增
	StepIndexModeManual = "manual" // 模板分配
)

// 步骤状态枚举
const (
	StepStatusPending     = "pending"     // 待执行
	StepStatusRunning     = "running"     // 执行中
	StepStatusCompleted   = "completed"   // 已完成
	StepStatusFailed      = "failed"      // 执行失败
	StepStatusCompensated = "compensated" // 已补偿
)

// 补偿状态枚举
const (
	CompensationStatusUninitialized = "uninitialized" // 未初始化
	CompensationStatusPending       = "pending"       // 待补偿
	CompensationStatusRunning       = "running"       // 补偿中
	CompensationStatusCompleted     = "completed"     // 补偿完成
	CompensationStatusFailed        = "failed"        // 补偿失败
	CompensationStatusDelay         = "delay"         // 补偿延期上报
)

// 补偿重试配置
const (
	DefaultMaxRetries           = 3                // 默认最大重试次数
	DefaultInitialRetryInterval = time.Second      // 默认初始重试间隔
	DefaultMaxRetryInterval     = 30 * time.Second // 默认最大重试间隔
	DefaultRetryMultiplier      = 2.0              // 默认指数退避倍数
	DefaultCompensationTimeout  = 30 * time.Second // 默认补偿超时时间
	// ReportCompensation 重试配置
	DefaultReportMaxRetries = 3                     // 默认 ReportCompensation 最大重试次数
	DefaultReportRetryDelay = 50 * time.Millisecond // 默认 ReportCompensation 基础重试延迟
	// 补偿窗口期配置
	DefaultCompensationWindowSec = 30 // 默认补偿窗口期（秒）
)

// 补偿执行模式
const (
	CompensationExecutionModeNone  = "none"  // 不执行补偿，仅标记状态
	CompensationExecutionModeSync  = "sync"  // 同步执行补偿
	CompensationExecutionModeAsync = "async" // 异步执行补偿
)
