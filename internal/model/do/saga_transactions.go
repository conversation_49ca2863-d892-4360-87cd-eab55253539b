// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SagaTransactions is the golang structure of table saga_transactions for DAO operations like Where/Data.
type SagaTransactions struct {
	g.Meta                `orm:"table:saga_transactions, do:true"`
	SagaId                interface{} // Saga 事务唯一 ID（UUID）
	Name                  interface{} // Saga 名称
	SagaStatus            interface{} // Saga 状态
	FailReason            interface{} // 失败原因（执行或补偿失败）
	StepIndexMode         interface{} // 步骤索引分配模式（manual：模板分配，auto：自动递增）
	StepTemplates         interface{} // 当使用 manual 模式时，保存步骤模板（服务名、动作名、顺序）
	CurStepIndex          interface{} // 当使用 auto 模式时，当前自增的 step_index
	CompensationWindowSec interface{} // 补偿窗口期（秒），commit/rollback后仍接受补偿上报的时间窗口
	CreatedAt             *gtime.Time //
	UpdatedAt             *gtime.Time //
	DeletedAt             *gtime.Time //
}
