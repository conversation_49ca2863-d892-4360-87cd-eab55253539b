// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SagaSteps is the golang structure of table saga_steps for DAO operations like Where/Data.
type SagaSteps struct {
	g.Meta              `orm:"table:saga_steps, do:true"`
	StepId              interface{} // 步骤ID
	SagaId              interface{} // 关联 Saga 事务
	Action              interface{} // 步骤名称
	StepIndex           interface{} // 步骤顺序
	ServiceName         interface{} // 服务名称（业务服务）
	ContextData         interface{} // 正向执行的上下文参数（由服务自己使用）
	CompensationContext interface{} // 补偿需要的上下文参数（由 orchestrator 使用）
	CompensateEndpoint  interface{} // 补偿接口 URL（如 http://order-svc/saga/compensate/CancelOrder）
	CompensationStatus  interface{} // 补偿执行状态
	LastError           interface{} // 失败信息或补偿失败信息
	RetryCount          interface{} // 补偿重试次数
	CreatedAt           *gtime.Time //
	UpdatedAt           *gtime.Time //
	DeletedAt           *gtime.Time //
}
