package model

import (
	"time"

	"github.com/gogf/gf/v2/os/gtime"
)

// SagaStepTemplate 步骤模板定义（简化版本）
type SagaStepTemplate struct {
	StepIndex   int    `json:"step_index"`  // 步骤索引
	Service     string `json:"service"`     // 服务名称
	Action      string `json:"action"`      // 动作名称
	Description string `json:"description"` // 描述
}

// CreateSagaTransactionInput 创建 Saga 事务输入参数
type CreateSagaTransactionInput struct {
	Name                  string             `json:"name"`
	TimeoutSec            int                `json:"timeoutSec"`            // 超时时间（秒）
	StepIndexMode         string             `json:"stepIndexMode"`         // StepIndex管理模式
	StepTemplates         []SagaStepTemplate `json:"stepTemplates"`         // 步骤模板（仅manual模式使用）
	CompensationWindowSec int                `json:"compensationWindowSec"` // 补偿窗口期（秒），默认30秒
}

// CreateSagaTransactionOutput 创建 Saga 事务输出参数
type CreateSagaTransactionOutput struct {
	SagaId    string      `json:"sagaId"`    // Saga 事务 ID
	Name      string      `json:"name"`      // Saga 名称
	Status    string      `json:"status"`    // 当前状态
	CreatedAt *gtime.Time `json:"createdAt"` // 创建时间
}

// ReportCompensationInput 上报补偿操作输入参数
type ReportCompensationInput struct {
	SagaId              string `json:"sagaId"`
	Action              string `json:"action"`
	ServiceName         string `json:"serviceName"`
	ContextData         string `json:"contextData"`         // 正向执行的上下文参数
	CompensationContext string `json:"compensationContext"` // 补偿需要的上下文参数
	CompensateEndpoint  string `json:"compensateEndpoint"`  // 补偿接口 URL
}

// ReportCompensationOutput 上报补偿操作输出参数
type ReportCompensationOutput struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 响应消息
}

// GetSagaTransactionInput 获取 Saga 事务输入参数
type GetSagaTransactionInput struct {
	SagaId string `json:"sagaId"`
}

// GetSagaTransactionOutput 获取 Saga 事务输出参数
type GetSagaTransactionOutput struct {
	SagaId      string      `json:"sagaId"`      // Saga 事务 ID
	Name        string      `json:"name"`        // Saga 名称
	Status      string      `json:"status"`      // 当前状态
	CurrentStep string      `json:"currentStep"` // 当前步骤
	RetryCount  int         `json:"retryCount"`  // 重试次数
	CreatedAt   *gtime.Time `json:"createdAt"`   // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"`   // 更新时间
}

// CommitSagaTransactionInput 提交 Saga 事务输入参数
type CommitSagaTransactionInput struct {
	SagaId string `json:"sagaId"`
}

// CommitSagaTransactionOutput 提交 Saga 事务输出参数
type CommitSagaTransactionOutput struct {
	Success        bool        `json:"success"`        // 是否成功
	Message        string      `json:"message"`        // 响应消息
	SagaId         string      `json:"sagaId"`         // Saga 事务 ID
	CompletedSteps int         `json:"completedSteps"` // 已完成步骤数
	ExpectedSteps  int         `json:"expectedSteps"`  // 预期步骤数
	NewStatus      string      `json:"newStatus"`      // 新的状态
	CompletedAt    *gtime.Time `json:"completedAt"`    // 完成时间
}

// StartRollbackInput 开始回滚输入参数
type StartRollbackInput struct {
	SagaId     string `json:"sagaId"`
	FailReason string `json:"failReason"` // 回滚原因
	FailedStep string `json:"failedStep"` // 失败的步骤名称（可选）
}

// StartRollbackOutput 开始回滚输出参数
type StartRollbackOutput struct {
	Success           bool        `json:"success"`           // 是否成功
	Message           string      `json:"message"`           // 响应消息
	SagaId            string      `json:"sagaId"`            // Saga 事务 ID
	NewStatus         string      `json:"newStatus"`         // 新的状态（compensating）
	CompensationSteps []StepInfo  `json:"compensationSteps"` // 补偿步骤详情
	TotalStepsToRoll  int         `json:"totalStepsToRoll"`  // 需要回滚的步骤总数
	StartedAt         *gtime.Time `json:"startedAt"`         // 回滚开始时间
}

// StepInfo 步骤信息
type StepInfo struct {
	StepId             string `json:"stepId"`             // 步骤ID
	StepIndex          int    `json:"stepIndex"`          // 步骤索引
	Action             string `json:"action"`             // 步骤名称
	ServiceName        string `json:"serviceName"`        // 服务名称
	CompensateEndpoint string `json:"compensateEndpoint"` // 补偿端点
	CompensationStatus string `json:"compensationStatus"` // 补偿状态
}

// CompensationStatistics 补偿统计信息
type CompensationStatistics struct {
	Total         int `json:"total"`         // 总步骤数
	Completed     int `json:"completed"`     // 已完成补偿数
	Failed        int `json:"failed"`        // 失败补偿数
	Uninitialized int `json:"uninitialized"` // 未初始化
	Pending       int `json:"pending"`       // 待处理补偿数
	Running       int `json:"running"`       // 运行中补偿数
	Delay         int `json:"delay"`         // 延期上报补偿数
}

// ExecuteCompensationInput 执行补偿操作输入参数
type ExecuteCompensationInput struct {
	SagaId    string        `json:"sagaId"`
	StepId    string        `json:"stepId"`
	StepIndex int           `json:"stepIndex"`
	Timeout   time.Duration `json:"timeout"` // 补偿操作超时时间（毫秒）
}

// ExecuteCompensationOutput 执行补偿操作输出参数
type ExecuteCompensationOutput struct {
	StepId             string      `json:"stepId"`             // 步骤ID
	Success            bool        `json:"success"`            // 是否成功执行补偿调用
	Message            string      `json:"message"`            // 响应消息
	SagaId             string      `json:"sagaId"`             // Saga 事务 ID
	StepIndex          int         `json:"stepIndex"`          // 步骤索引
	Action             string      `json:"action"`             // 步骤名称
	ServiceName        string      `json:"serviceName"`        // 服务名称
	CompensateEndpoint string      `json:"compensateEndpoint"` // 补偿端点
	CompensationStatus string      `json:"compensationStatus"` // 补偿状态
	RetryCount         int         `json:"retryCount"`         // 重试次数
	ExecutedAt         *gtime.Time `json:"executedAt"`         // 执行时间
	LastError          string      `json:"lastError"`          // 最后的错误信息
}

// ReportCompensationResultInput 上报补偿结果输入参数
type ReportCompensationResultInput struct {
	SagaId     string `json:"sagaId"`
	StepIndex  int    `json:"stepIndex"`
	Status     string `json:"status"`
	Message    string `json:"message"`    // 补偿执行消息
	Error      string `json:"error"`      // 补偿失败的错误信息
	Duration   int    `json:"duration"`   // 补偿执行时长（毫秒）
	RetryCount int    `json:"retryCount"` // 当前重试次数
}

// ReportCompensationResultOutput 上报补偿结果输出参数
type ReportCompensationResultOutput struct {
	Success        bool        `json:"success"`        // 是否成功
	Message        string      `json:"message"`        // 响应消息
	SagaId         string      `json:"sagaId"`         // Saga 事务 ID
	StepIndex      int         `json:"stepIndex"`      // 步骤索引
	NewStatus      string      `json:"newStatus"`      // 新的补偿状态
	NextAction     string      `json:"nextAction"`     // 下一步动作（continue_rollback, retry_compensation, rollback_completed, rollback_failed）
	UpdatedAt      *gtime.Time `json:"updatedAt"`      // 更新时间
	ShouldRetry    bool        `json:"shouldRetry"`    // 是否应该重试
	MaxRetries     int         `json:"maxRetries"`     // 最大重试次数
	CurrentRetries int         `json:"currentRetries"` // 当前重试次数
}

// CheckRollbackStatusInput 检查回滚状态输入参数
type CheckRollbackStatusInput struct {
	SagaId string `json:"sagaId"`
}

// CheckRollbackStatusOutput 检查回滚状态输出参数
type CheckRollbackStatusOutput struct {
	Success                    bool                    `json:"success"`                    // 是否成功
	Message                    string                  `json:"message"`                    // 响应消息
	SagaId                     string                  `json:"sagaId"`                     // Saga 事务 ID
	CurrentStatus              string                  `json:"currentStatus"`              // 当前Saga状态
	TotalStepsToRoll           int                     `json:"totalStepsToRoll"`           // 需要回滚的步骤总数
	CompletedCompensations     int                     `json:"completedCompensations"`     // 已完成补偿的步骤数
	FailedCompensations        int                     `json:"failedCompensations"`        // 补偿失败的步骤数
	UninitializedCompensations int                     `json:"uninitializedCompensations"` // 未初始化步骤数
	PendingCompensations       int                     `json:"pendingCompensations"`       // 待补偿的步骤数
	RunningCompensations       int                     `json:"runningCompensations"`       // 运行中补偿的步骤数
	DelayCompensations         int                     `json:"delayCompensations"`         // 延期上报补偿的步骤数
	CompensationStats          *CompensationStatistics `json:"compensationStats"`          // 详细补偿统计信息
	IsRollbackCompleted        bool                    `json:"isRollbackCompleted"`        // 回滚是否完成
	IsRollbackFailed           bool                    `json:"isRollbackFailed"`           // 回滚是否失败
	CompensationSteps          []StepInfo              `json:"compensationSteps"`          // 补偿步骤详情
	LastUpdatedAt              *gtime.Time             `json:"lastUpdatedAt"`              // 最后更新时间
	FailReason                 string                  `json:"failReason"`                 // 失败原因
}

// RollbackSagaTransactionInput 回滚分布式事务输入参数
type RollbackSagaTransactionInput struct {
	SagaId        string `json:"sagaId"`
	FailReason    string `json:"failReason"`    // 回滚原因
	FailedStep    string `json:"failedStep"`    // 失败的步骤名称（可选）
	ExecutionMode string `json:"executionMode"` // 执行模式：none/sync/async
}

// RollbackSagaTransactionOutput 回滚 Saga 事务输出参数（统一接口）
type RollbackSagaTransactionOutput struct {
	Success             bool        `json:"success"`             // 是否成功启动回滚
	Message             string      `json:"message"`             // 响应消息
	SagaId              string      `json:"sagaId"`              // Saga 事务 ID
	NewStatus           string      `json:"newStatus"`           // 新的状态
	IsRollbackCompleted bool        `json:"isRollbackCompleted"` // 回滚是否完成
	StartedAt           *gtime.Time `json:"startedAt"`           // 回滚开始时间
	CompletedAt         *gtime.Time `json:"completedAt"`         // 回滚完成时间（如果已完成）
	FailReason          string      `json:"failReason"`          // 失败原因
}

// RecoveryStatusOutput 恢复服务状态输出
type RecoveryStatusOutput struct {
	IsRecoveryRunning    bool          `json:"isRecoveryRunning"`    // 恢复服务是否运行中
	TaskTimeout          time.Duration `json:"taskTimeout"`          // 任务超时时间
	RecoveryInterval     time.Duration `json:"recoveryInterval"`     // 恢复检查间隔
	MaxConcurrentTasks   int           `json:"maxConcurrentTasks"`   // 最大并发任务数
	TotalUnfinishedTasks int           `json:"totalUnfinishedTasks"` // 总未完成任务数
	PendingCompensations int           `json:"pendingCompensations"` // 待处理补偿数
	RunningCompensations int           `json:"runningCompensations"` // 运行中补偿数
	DelayCompensations   int           `json:"delayCompensations"`   // 延期上报补偿数
	LastRecoveryTime     *gtime.Time   `json:"lastRecoveryTime"`     // 最后恢复时间
}
