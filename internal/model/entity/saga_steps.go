// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SagaSteps is the golang structure for table saga_steps.
type SagaSteps struct {
	StepId              string      `json:"stepId"              orm:"step_id"              description:"步骤ID"`                                                     // 步骤ID
	SagaId              string      `json:"sagaId"              orm:"saga_id"              description:"关联 Saga 事务"`                                               // 关联 Saga 事务
	Action              string      `json:"action"              orm:"action"               description:"步骤名称"`                                                     // 步骤名称
	StepIndex           int         `json:"stepIndex"           orm:"step_index"           description:"步骤顺序"`                                                     // 步骤顺序
	ServiceName         string      `json:"serviceName"         orm:"service_name"         description:"服务名称（业务服务）"`                                               // 服务名称（业务服务）
	ContextData         string      `json:"contextData"         orm:"context_data"         description:"正向执行的上下文参数（由服务自己使用）"`                                      // 正向执行的上下文参数（由服务自己使用）
	CompensationContext string      `json:"compensationContext" orm:"compensation_context" description:"补偿需要的上下文参数（由 orchestrator 使用）"`                            // 补偿需要的上下文参数（由 orchestrator 使用）
	CompensateEndpoint  string      `json:"compensateEndpoint"  orm:"compensate_endpoint"  description:"补偿接口 URL（如 http://order-svc/saga/compensate/CancelOrder）"` // 补偿接口 URL（如 http://order-svc/saga/compensate/CancelOrder）
	CompensationStatus  string      `json:"compensationStatus"  orm:"compensation_status"  description:"补偿执行状态"`                                                   // 补偿执行状态
	LastError           string      `json:"lastError"           orm:"last_error"           description:"失败信息或补偿失败信息"`                                              // 失败信息或补偿失败信息
	RetryCount          int         `json:"retryCount"          orm:"retry_count"          description:"补偿重试次数"`                                                   // 补偿重试次数
	CreatedAt           *gtime.Time `json:"createdAt"           orm:"created_at"           description:""`                                                         //
	UpdatedAt           *gtime.Time `json:"updatedAt"           orm:"updated_at"           description:""`                                                         //
	DeletedAt           *gtime.Time `json:"deletedAt"           orm:"deleted_at"           description:""`                                                         //
}
