// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SagaTransactions is the golang structure for table saga_transactions.
type SagaTransactions struct {
	SagaId                string      `json:"sagaId"                orm:"saga_id"                 description:"Saga 事务唯一 ID（UUID）"`                    // Saga 事务唯一 ID（UUID）
	Name                  string      `json:"name"                  orm:"name"                    description:"Saga 名称"`                               // Saga 名称
	SagaStatus            string      `json:"sagaStatus"            orm:"saga_status"             description:"Saga 状态"`                               // Saga 状态
	FailReason            string      `json:"failReason"            orm:"fail_reason"             description:"失败原因（执行或补偿失败）"`                         // 失败原因（执行或补偿失败）
	StepIndexMode         string      `json:"stepIndexMode"         orm:"step_index_mode"         description:"步骤索引分配模式（manual：模板分配，auto：自动递增）"`       // 步骤索引分配模式（manual：模板分配，auto：自动递增）
	StepTemplates         string      `json:"stepTemplates"         orm:"step_templates"          description:"当使用 manual 模式时，保存步骤模板（服务名、动作名、顺序）"`     // 当使用 manual 模式时，保存步骤模板（服务名、动作名、顺序）
	CurStepIndex          int         `json:"curStepIndex"          orm:"cur_step_index"          description:"当使用 auto 模式时，当前自增的 step_index"`         // 当使用 auto 模式时，当前自增的 step_index
	CompensationWindowSec int         `json:"compensationWindowSec" orm:"compensation_window_sec" description:"补偿窗口期（秒），commit/rollback后仍接受补偿上报的时间窗口"` // 补偿窗口期（秒），commit/rollback后仍接受补偿上报的时间窗口
	CreatedAt             *gtime.Time `json:"createdAt"             orm:"created_at"              description:""`                                      //
	UpdatedAt             *gtime.Time `json:"updatedAt"             orm:"updated_at"              description:""`                                      //
	DeletedAt             *gtime.Time `json:"deletedAt"             orm:"deleted_at"              description:""`                                      //
}
