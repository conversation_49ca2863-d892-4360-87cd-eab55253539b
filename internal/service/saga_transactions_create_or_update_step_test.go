package service

import (
	"context"
	"fmt"
	"saga/internal/dao"
	"saga/internal/model"
	"saga/internal/model/entity"
	"sync"
	"testing"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TestSagaTransactionsService_CreateOrUpdateStep 测试 createOrUpdateStep 方法的各种场景
func TestSagaTransactionsService_CreateOrUpdateStep(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactions()

	t.Run("场景1：Auto模式下创建新步骤", func(t *testing.T) {
		// 1. 创建 Auto 模式的 Saga 事务
		createInput := &model.CreateSagaTransactionInput{
			Name:          "Auto模式创建步骤测试",
			TimeoutSec:    30,
			StepIndexMode: "auto",
		}

		output, err := service.CreateSagaTransaction(ctx, createInput)
		if err != nil {
			t.Fatalf("创建 Saga 事务失败: %v", err)
		}
		sagaID := output.SagaId

		// 清理测试数据
		defer cleanupTestData(t, sagaID)

		// 2. 在事务中调用 createOrUpdateStep
		var stepOutput *entity.SagaSteps
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// 获取 Saga 事务（使用行级锁确保并发安全）
			sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaID)
			if err != nil {
				return err
			}

			stepInput := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              "create_order",
				ServiceName:         "order-service",
				ContextData:         `{"order_id": "12345", "amount": 100}`,
				CompensationContext: `{"compensate_order": "12345"}`,
				CompensateEndpoint:  "http://order-service/compensate",
			}

			stepOutput, err = service.createOrUpdateStep(ctx, tx, sagaTransaction, stepInput)
			return err
		})

		if err != nil {
			t.Fatalf("createOrUpdateStep 失败: %v", err)
		}

		// 3. 验证返回结果
		if stepOutput == nil {
			t.Fatal("stepOutput 不应该为 nil")
		}

		if stepOutput.StepId == "" {
			t.Error("StepId 不应该为空")
		}

		if stepOutput.StepIndex != 1 {
			t.Errorf("期望 StepIndex 为 1，实际为 %d", stepOutput.StepIndex)
		}

		// 4. 验证数据库中的记录
		dbStep, err := dao.SagaSteps.FindByStepId(ctx, stepOutput.StepId)
		if err != nil {
			t.Fatalf("查询步骤失败: %v", err)
		}

		if dbStep.Action != "create_order" {
			t.Errorf("期望 Action 为 'create_order'，实际为 '%s'", dbStep.Action)
		}

		if dbStep.ServiceName != "order-service" {
			t.Errorf("期望 ServiceName 为 'order-service'，实际为 '%s'", dbStep.ServiceName)
		}

		if dbStep.StepIndex != 1 {
			t.Errorf("期望数据库中 StepIndex 为 1，实际为 %d", dbStep.StepIndex)
		}

		// 5. 验证 Saga 事务的 CurStepIndex 已更新
		updatedSagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
		if err != nil {
			t.Fatalf("查询 Saga 事务失败: %v", err)
		}

		if updatedSagaTransaction.CurStepIndex != 1 {
			t.Errorf("期望 Saga CurStepIndex 为 1，实际为 %d", updatedSagaTransaction.CurStepIndex)
		}

		t.Log("✅ Auto模式下创建新步骤测试通过")
	})

	t.Run("场景2：Auto模式下幂等性更新", func(t *testing.T) {
		// 1. 创建 Auto 模式的 Saga 事务
		createInput := &model.CreateSagaTransactionInput{
			Name:          "Auto模式幂等性测试",
			TimeoutSec:    30,
			StepIndexMode: "auto",
		}

		output, err := service.CreateSagaTransaction(ctx, createInput)
		if err != nil {
			t.Fatalf("创建 Saga 事务失败: %v", err)
		}
		sagaID := output.SagaId
		// 清理测试数据
		//defer cleanupTestData(t, sagaID)

		// 2. 第一次创建步骤
		var firstStepOutput *entity.SagaSteps
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// 获取 Saga 事务（使用行级锁确保并发安全）
			sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaID)
			if err != nil {
				return err
			}

			stepInput := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              "process_payment",
				ServiceName:         "payment-service",
				ContextData:         `{"payment_id": "pay-123", "amount": 100}`,
				CompensationContext: `{"compensate_payment": "pay-123"}`,
				CompensateEndpoint:  "http://payment-service/compensate",
			}

			firstStepOutput, err = service.createOrUpdateStep(ctx, tx, sagaTransaction, stepInput)
			return err
		})

		if err != nil {
			t.Fatalf("第一次 createOrUpdateStep 失败: %v", err)
		}

		// 3. 第二次调用相同的步骤（幂等性测试）
		var secondStepOutput *entity.SagaSteps
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// 获取 Saga 事务（使用行级锁确保并发安全）
			sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaID)
			if err != nil {
				return err
			}

			stepInput := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              "process_payment",
				ServiceName:         "payment-service",
				ContextData:         `{"payment_id": "pay-123", "amount": 200, "updated": true}`, // 修改内容
				CompensationContext: `{"compensate_payment": "pay-123", "updated": true}`,
				CompensateEndpoint:  "http://payment-service/compensate-v2", // 修改端点
			}

			secondStepOutput, err = service.createOrUpdateStep(ctx, tx, sagaTransaction, stepInput)
			return err
		})

		if err != nil {
			t.Fatalf("第二次 createOrUpdateStep 失败: %v", err)
		}

		// 4. 验证幂等性结果
		if firstStepOutput.StepId != secondStepOutput.StepId {
			t.Errorf("幂等性调用应该返回相同的 StepId，第一次: %s，第二次: %s",
				firstStepOutput.StepId, secondStepOutput.StepId)
		}

		if firstStepOutput.StepIndex != secondStepOutput.StepIndex {
			t.Errorf("幂等性调用应该返回相同的 StepIndex，第一次: %d，第二次: %d",
				firstStepOutput.StepIndex, secondStepOutput.StepIndex)
		}

		// 注意：createOrUpdateStep 返回的是 entity.SagaSteps，没有 IsNewStep 字段
		// 我们通过其他方式验证幂等性：相同的 StepId 和 StepIndex

		// 5. 验证数据库中的内容已更新
		dbStep, err := dao.SagaSteps.FindByStepId(ctx, secondStepOutput.StepId)
		if err != nil {
			t.Fatalf("查询步骤失败: %v", err)
		}

		// JSON 字段顺序可能不同，检查关键字段
		if !contains(dbStep.ContextData, `"amount": 200`) ||
			!contains(dbStep.ContextData, `"updated": true`) ||
			!contains(dbStep.ContextData, `"payment_id": "pay-123"`) {
			t.Errorf("期望 ContextData 已更新，实际为: %s", dbStep.ContextData)
		}

		if dbStep.CompensateEndpoint != "http://payment-service/compensate-v2" {
			t.Errorf("期望 CompensateEndpoint 已更新，实际为: %s", dbStep.CompensateEndpoint)
		}

		// 6. 验证 Saga 事务的 CurStepIndex 没有重复递增
		sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
		if err != nil {
			t.Fatalf("查询 Saga 事务失败: %v", err)
		}

		if sagaTransaction.CurStepIndex != 1 {
			t.Errorf("期望 Saga CurStepIndex 仍为 1，实际为 %d", sagaTransaction.CurStepIndex)
		}

		t.Log("✅ Auto模式下幂等性更新测试通过")
	})

	t.Run("场景3：Manual模式下创建步骤", func(t *testing.T) {
		// 1. 创建 Manual 模式的 Saga 事务
		stepTemplates := []model.SagaStepTemplate{
			{StepIndex: 1, Action: "create_order", Service: "order-service", Description: "创建订单"},
			{StepIndex: 2, Action: "process_payment", Service: "payment-service", Description: "处理支付"},
			{StepIndex: 3, Action: "reserve_inventory", Service: "inventory-service", Description: "预留库存"},
		}

		createInput := &model.CreateSagaTransactionInput{
			Name:          "Manual模式创建步骤测试",
			TimeoutSec:    30,
			StepIndexMode: "manual",
			StepTemplates: stepTemplates,
		}

		output, err := service.CreateSagaTransaction(ctx, createInput)
		if err != nil {
			t.Fatalf("创建 Saga 事务失败: %v", err)
		}
		sagaID := output.SagaId

		// 清理测试数据
		defer cleanupTestData(t, sagaID)

		// 2. 创建第二个步骤（跳过第一个）
		var stepOutput *entity.SagaSteps
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// 获取 Saga 事务（使用行级锁确保并发安全）
			sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaID)
			if err != nil {
				return err
			}

			stepInput := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              "process_payment",
				ServiceName:         "payment-service",
				ContextData:         `{"payment_id": "pay-456"}`,
				CompensationContext: `{"compensate_payment": "pay-456"}`,
				CompensateEndpoint:  "http://payment-service/compensate",
			}

			stepOutput, err = service.createOrUpdateStep(ctx, tx, sagaTransaction, stepInput)
			return err
		})

		if err != nil {
			t.Fatalf("createOrUpdateStep 失败: %v", err)
		}

		// 3. 验证 Manual 模式下的索引分配
		if stepOutput.StepIndex != 2 {
			t.Errorf("Manual 模式下期望 StepIndex 为 2，实际为 %d", stepOutput.StepIndex)
		}

		// 4. 验证数据库中的记录
		dbStep, err := dao.SagaSteps.FindByStepId(ctx, stepOutput.StepId)
		if err != nil {
			t.Fatalf("查询步骤失败: %v", err)
		}

		if dbStep.StepIndex != 2 {
			t.Errorf("期望数据库中 StepIndex 为 2，实际为 %d", dbStep.StepIndex)
		}

		// 5. 验证 Saga 事务的 CurStepIndex 在 Manual 模式下不自动更新
		sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
		if err != nil {
			t.Fatalf("查询 Saga 事务失败: %v", err)
		}

		// Manual 模式下 CurStepIndex 应该保持为 0 或不自动递增
		if sagaTransaction.CurStepIndex != 0 {
			t.Logf("Manual 模式下 CurStepIndex: %d", sagaTransaction.CurStepIndex)
		}

		t.Log("✅ Manual模式下创建步骤测试通过")
	})

	t.Run("场景4：验证幂等性调用的内容更新", func(t *testing.T) {
		// 1. 创建 Auto 模式的 Saga 事务
		createInput := &model.CreateSagaTransactionInput{
			Name:          "内容更新测试",
			TimeoutSec:    30,
			StepIndexMode: "auto",
		}

		output, err := service.CreateSagaTransaction(ctx, createInput)
		if err != nil {
			t.Fatalf("创建 Saga 事务失败: %v", err)
		}
		sagaID := output.SagaId

		// 清理测试数据
		defer cleanupTestData(t, sagaID)

		// 2. 第一次创建步骤
		var firstStepOutput *entity.SagaSteps
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaID)
			if err != nil {
				return err
			}

			stepInput := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              "update_test",
				ServiceName:         "test-service",
				ContextData:         `{"version": 1, "data": "original"}`,
				CompensationContext: `{"compensate_version": 1}`,
				CompensateEndpoint:  "http://test-service/compensate",
			}

			firstStepOutput, err = service.createOrUpdateStep(ctx, tx, sagaTransaction, stepInput)
			return err
		})

		if err != nil {
			t.Fatalf("第一次 createOrUpdateStep 失败: %v", err)
		}

		// 3. 第二次调用，更新内容
		var secondStepOutput *entity.SagaSteps
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaID)
			if err != nil {
				return err
			}

			stepInput := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              "update_test",
				ServiceName:         "test-service",
				ContextData:         `{"version": 2, "data": "updated", "timestamp": "2024-01-01"}`,
				CompensationContext: `{"compensate_version": 2, "rollback": true}`,
				CompensateEndpoint:  "http://test-service/compensate-v2",
			}

			secondStepOutput, err = service.createOrUpdateStep(ctx, tx, sagaTransaction, stepInput)
			return err
		})

		if err != nil {
			t.Fatalf("第二次 createOrUpdateStep 失败: %v", err)
		}

		// 4. 验证幂等性：相同的 StepId 和 StepIndex
		if firstStepOutput.StepId != secondStepOutput.StepId {
			t.Errorf("幂等性调用应该返回相同的 StepId，第一次: %s，第二次: %s",
				firstStepOutput.StepId, secondStepOutput.StepId)
		}

		if firstStepOutput.StepIndex != secondStepOutput.StepIndex {
			t.Errorf("幂等性调用应该返回相同的 StepIndex，第一次: %d，第二次: %d",
				firstStepOutput.StepIndex, secondStepOutput.StepIndex)
		}

		// 5. 验证内容已更新（JSON 字段顺序可能不同）
		if !contains(secondStepOutput.ContextData, `"version": 2`) ||
			!contains(secondStepOutput.ContextData, `"data": "updated"`) ||
			!contains(secondStepOutput.ContextData, `"timestamp": "2024-01-01"`) {
			t.Errorf("期望 ContextData 已更新，实际为: %s", secondStepOutput.ContextData)
		}

		if !contains(secondStepOutput.CompensationContext, `"compensate_version": 2`) ||
			!contains(secondStepOutput.CompensationContext, `"rollback": true`) {
			t.Errorf("期望 CompensationContext 已更新，实际为: %s", secondStepOutput.CompensationContext)
		}

		if secondStepOutput.CompensateEndpoint != "http://test-service/compensate-v2" {
			t.Errorf("期望 CompensateEndpoint 已更新，实际为: %s", secondStepOutput.CompensateEndpoint)
		}

		// 6. 验证数据库中的记录也已更新
		dbStep, err := dao.SagaSteps.FindByStepId(ctx, secondStepOutput.StepId)
		if err != nil {
			t.Fatalf("查询步骤失败: %v", err)
		}

		if !contains(dbStep.ContextData, `"version": 2`) ||
			!contains(dbStep.ContextData, `"data": "updated"`) ||
			!contains(dbStep.ContextData, `"timestamp": "2024-01-01"`) {
			t.Errorf("数据库中期望 ContextData 已更新，实际为: %s", dbStep.ContextData)
		}

		t.Log("✅ 验证幂等性调用的内容更新测试通过")
		t.Logf("   StepId: %s", secondStepOutput.StepId[:8])
		t.Logf("   StepIndex: %d", secondStepOutput.StepIndex)
		t.Logf("   内容已正确更新")
	})

	t.Run("场景5：高并发创建不同步骤", func(t *testing.T) {
		// 1. 创建 Auto 模式的 Saga 事务
		createInput := &model.CreateSagaTransactionInput{
			Name:          "高并发创建不同步骤测试",
			TimeoutSec:    30,
			StepIndexMode: "auto",
		}

		output, err := service.CreateSagaTransaction(ctx, createInput)
		if err != nil {
			t.Fatalf("创建 Saga 事务失败: %v", err)
		}
		sagaID := output.SagaId
		// 清理测试数据
		defer cleanupTestData(t, sagaID)
		// 2. 高并发创建不同的步骤
		concurrency := 10
		stepsPerGoroutine := 5
		totalSteps := concurrency * stepsPerGoroutine

		var wg sync.WaitGroup
		var mu sync.Mutex
		stepOutputs := make([]*entity.SagaSteps, 0, totalSteps)
		errors := make([]error, 0)

		startTime := time.Now()

		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				for j := 0; j < stepsPerGoroutine; j++ {
					action := fmt.Sprintf("action_%d_%d", goroutineID, j)
					serviceName := fmt.Sprintf("service-%d-%d", goroutineID, j)

					err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
						sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaID)
						if err != nil {
							return err
						}

						stepInput := &model.ReportCompensationInput{
							SagaId:              sagaID,
							Action:              action,
							ServiceName:         serviceName,
							ContextData:         fmt.Sprintf(`{"goroutine": %d, "step": %d}`, goroutineID, j),
							CompensationContext: fmt.Sprintf(`{"compensate_goroutine": %d, "step": %d}`, goroutineID, j),
							CompensateEndpoint:  fmt.Sprintf("http://%s/compensate", serviceName),
						}

						stepOutput, stepErr := service.createOrUpdateStep(ctx, tx, sagaTransaction, stepInput)
						if stepErr != nil {
							return stepErr
						}

						mu.Lock()
						stepOutputs = append(stepOutputs, stepOutput)
						mu.Unlock()

						return nil
					})

					if err != nil {
						mu.Lock()
						errors = append(errors, fmt.Errorf("goroutine %d step %d: %v", goroutineID, j, err))
						mu.Unlock()
					}

					// 添加小延迟增加并发冲突
					time.Sleep(time.Millisecond * 1)
				}
			}(i)
		}

		// 等待所有 goroutine 完成
		wg.Wait()
		duration := time.Since(startTime)

		// 3. 验证结果
		if len(errors) > 0 {
			for _, err := range errors {
				t.Logf("并发创建错误: %v", err)
			}
			t.Fatalf("并发创建过程中出现 %d 个错误", len(errors))
		}

		if len(stepOutputs) != totalSteps {
			t.Fatalf("期望创建 %d 个步骤，实际创建 %d 个", totalSteps, len(stepOutputs))
		}

		// 4. 验证步骤索引的唯一性和连续性
		indexMap := make(map[int]bool)
		for _, stepOutput := range stepOutputs {
			if indexMap[stepOutput.StepIndex] {
				t.Errorf("发现重复的步骤索引: %d", stepOutput.StepIndex)
			}
			indexMap[stepOutput.StepIndex] = true

			if stepOutput.StepIndex < 1 || stepOutput.StepIndex > totalSteps {
				t.Errorf("步骤索引超出范围: %d (应该在 1-%d 之间)", stepOutput.StepIndex, totalSteps)
			}
		}

		// 验证索引连续性
		for i := 1; i <= totalSteps; i++ {
			if !indexMap[i] {
				t.Errorf("缺少步骤索引: %d", i)
			}
		}

		// 5. 验证 Saga 事务的 CurStepIndex
		sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
		if err != nil {
			t.Fatalf("查询 Saga 事务失败: %v", err)
		}

		if sagaTransaction.CurStepIndex != totalSteps {
			t.Errorf("期望 Saga CurStepIndex 为 %d，实际为 %d", totalSteps, sagaTransaction.CurStepIndex)
		}

		t.Logf("✅ 高并发创建不同步骤测试通过")
		t.Logf("   总耗时: %v", duration)
		t.Logf("   成功创建 %d 个步骤", len(stepOutputs))
		t.Logf("   索引范围: 1-%d", totalSteps)
		t.Logf("   Saga CurStepIndex: %d", sagaTransaction.CurStepIndex)
		t.Logf("   平均每步耗时: %v", duration/time.Duration(totalSteps))
	})

	t.Run("场景6：高并发创建相同步骤（幂等性压力测试）", func(t *testing.T) {
		// 1. 创建 Auto 模式的 Saga 事务
		createInput := &model.CreateSagaTransactionInput{
			Name:          "高并发幂等性压力测试",
			TimeoutSec:    30,
			StepIndexMode: "auto",
		}

		output, err := service.CreateSagaTransaction(ctx, createInput)
		if err != nil {
			t.Fatalf("创建 Saga 事务失败: %v", err)
		}
		sagaID := output.SagaId
		// 清理测试数据
		defer cleanupTestData(t, sagaID)

		// 2. 高并发创建相同的步骤
		concurrency := 15
		duplicateCount := 8
		totalRequests := concurrency * duplicateCount

		// 定义要重复创建的步骤
		testSteps := []struct {
			action      string
			serviceName string
		}{
			{"create_order", "order-service"},
			{"process_payment", "payment-service"},
		}

		var wg sync.WaitGroup
		var mu sync.Mutex
		stepOutputs := make([]*entity.SagaSteps, 0, totalRequests)
		errors := make([]error, 0)

		startTime := time.Now()

		for stepIdx, testStep := range testSteps {
			for i := 0; i < concurrency; i++ {
				wg.Add(1)
				go func(goroutineID, stepIndex int, step struct {
					action      string
					serviceName string
				}) {
					defer wg.Done()

					for j := 0; j < duplicateCount; j++ {
						err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
							sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaID)
							if err != nil {
								return err
							}

							stepInput := &model.ReportCompensationInput{
								SagaId:              sagaID,
								Action:              step.action,
								ServiceName:         step.serviceName,
								ContextData:         fmt.Sprintf(`{"goroutine": %d, "duplicate": %d, "step": %d, "timestamp": %d}`, goroutineID, j, stepIndex, time.Now().Unix()),
								CompensationContext: fmt.Sprintf(`{"compensate_goroutine": %d, "duplicate": %d, "step": %d}`, goroutineID, j, stepIndex),
								CompensateEndpoint:  fmt.Sprintf("http://%s/compensate", step.serviceName),
							}

							stepOutput, stepErr := service.createOrUpdateStep(ctx, tx, sagaTransaction, stepInput)
							if stepErr != nil {
								return stepErr
							}

							mu.Lock()
							stepOutputs = append(stepOutputs, stepOutput)
							mu.Unlock()

							return nil
						})

						if err != nil {
							mu.Lock()
							errors = append(errors, fmt.Errorf("step %s goroutine %d duplicate %d: %v",
								step.action, goroutineID, j, err))
							mu.Unlock()
						}

						// 添加随机延迟增加并发冲突
						time.Sleep(time.Millisecond * time.Duration(j%3))
					}
				}(i, stepIdx, testStep)
			}
		}

		// 等待所有 goroutine 完成
		wg.Wait()
		duration := time.Since(startTime)

		// 3. 验证结果
		if len(errors) > 0 {
			for i, err := range errors {
				if i < 5 { // 只显示前5个错误
					t.Logf("并发幂等性错误: %v", err)
				}
			}
			if len(errors) > 5 {
				t.Logf("... 还有 %d 个错误", len(errors)-5)
			}
			t.Fatalf("并发幂等性测试过程中出现 %d 个错误", len(errors))
		}

		// 4. 统计步骤ID的分布
		stepIdMap := make(map[string]int) // stepId -> count
		for _, stepOutput := range stepOutputs {
			stepIdMap[stepOutput.StepId]++
		}

		// 5. 验证幂等性效果
		expectedUniqueSteps := len(testSteps) // 应该只有2个唯一步骤
		if len(stepIdMap) != expectedUniqueSteps {
			t.Errorf("期望只有 %d 个唯一的 StepId，实际有 %d 个", expectedUniqueSteps, len(stepIdMap))
		}

		expectedCallsPerStep := concurrency * duplicateCount // 每个步骤的期望调用次数
		for stepId, count := range stepIdMap {
			if count != expectedCallsPerStep {
				t.Errorf("StepId %s 期望被调用 %d 次，实际被调用 %d 次",
					stepId[:8], expectedCallsPerStep, count)
			}
		}

		// 6. 验证数据库中只有唯一的步骤
		allSteps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
		if err != nil {
			t.Fatalf("查询所有步骤失败: %v", err)
		}

		if len(allSteps) != expectedUniqueSteps {
			t.Errorf("数据库中期望只有 %d 个步骤，实际有 %d 个", expectedUniqueSteps, len(allSteps))
		}

		// 7. 验证 Saga 事务的 CurStepIndex
		sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
		if err != nil {
			t.Fatalf("查询 Saga 事务失败: %v", err)
		}

		if sagaTransaction.CurStepIndex != expectedUniqueSteps {
			t.Errorf("期望 Saga CurStepIndex 为 %d，实际为 %d", expectedUniqueSteps, sagaTransaction.CurStepIndex)
		}

		t.Logf("✅ 高并发创建相同步骤（幂等性压力测试）通过")
		t.Logf("   总耗时: %v", duration)
		t.Logf("   总请求数: %d", len(stepOutputs))
		t.Logf("   唯一步骤数: %d", len(stepIdMap))
		t.Logf("   幂等性效果: %.1f%% 的重复请求被正确处理",
			float64(len(stepOutputs)-len(stepIdMap))/float64(len(stepOutputs))*100)
		t.Logf("   数据库中实际步骤数: %d", len(allSteps))
		t.Logf("   Saga CurStepIndex: %d", sagaTransaction.CurStepIndex)
		t.Logf("   平均每请求耗时: %v", duration/time.Duration(len(stepOutputs)))
	})

}
