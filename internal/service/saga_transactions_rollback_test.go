package service

import (
	"context"
	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"testing"

	"github.com/gogf/gf/v2/frame/g"
)

// TestSagaTransactionsService_RollbackSagaTransaction 测试回滚分布式事务
func TestSagaTransactionsService_RollbackSagaTransaction(t *testing.T) {
	type args struct {
		ctx   context.Context
		input *model.RollbackSagaTransactionInput
	}
	tests := []struct {
		name    string
		s       *SagaTransactionsService
		args    args
		want    *model.RollbackSagaTransactionOutput
		wantErr bool
		setupDB func(sagaId string) // 设置测试数据的函数
	}{
		{
			name: "场景1：事务不存在",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.RollbackSagaTransactionInput{
					SagaId:        "non-existent-saga-id",
					FailReason:    "测试回滚",
					ExecutionMode: "none",
				},
			},
			want:    nil,
			wantErr: true,
			setupDB: func(sagaId string) {
				// 不需要设置数据，因为我们要测试不存在的情况
			},
		},
		{
			name: "场景2：无法回滚已完成的事务",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.RollbackSagaTransactionInput{
					SagaId:        "test-saga-completed",
					FailReason:    "尝试回滚已完成事务",
					ExecutionMode: "none",
				},
			},
			want: &model.RollbackSagaTransactionOutput{
				Success:   false,
				Message:   "无法回滚已完成的事务",
				SagaId:    "test-saga-completed",
				NewStatus: consts.SagaStatusCompleted,
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 2, consts.SagaStatusCompleted)
			},
		},
		{
			name: "场景3：待处理状态的事务无需回滚",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.RollbackSagaTransactionInput{
					SagaId:        "test-saga-pending-no-rollback",
					FailReason:    "尝试回滚待处理事务",
					ExecutionMode: "none",
				},
			},
			want: &model.RollbackSagaTransactionOutput{
				Success:   false,
				Message:   "待处理状态的事务无需回滚，因为还没有步骤上报",
				SagaId:    "test-saga-pending-no-rollback",
				NewStatus: consts.SagaStatusPending,
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)
			},
		},
		{
			name: "场景4：幂等性测试 - 事务已失败",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.RollbackSagaTransactionInput{
					SagaId:        "test-saga-failed-idempotent",
					FailReason:    "尝试回滚已失败事务",
					ExecutionMode: "none",
				},
			},
			want: &model.RollbackSagaTransactionOutput{
				Success:             true,
				Message:             "事务已失败（幂等性），回滚已完成",
				SagaId:              "test-saga-failed-idempotent",
				NewStatus:           consts.SagaStatusFailed,
				IsRollbackCompleted: true,
				FailReason:          "原始失败原因",
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 2, consts.SagaStatusFailed)
				// 创建一些已完成补偿的步骤
				createTestSagaStepWithCompensationStatus(t, sagaID, "step1", "service1", 1, consts.CompensationStatusCompleted)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step2", "service2", 2, consts.CompensationStatusCompleted)
				// 设置失败原因
				dao.SagaTransactions.Ctx(context.Background()).Where("saga_id", sagaID).Update(g.Map{
					"fail_reason": "原始失败原因",
				})
			},
		},
		{
			name: "场景4-1：幂等性测试 - 事务正在补偿",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.RollbackSagaTransactionInput{
					SagaId:        "test-saga-compensating-idempotent",
					FailReason:    "尝试回滚正在补偿的事务",
					ExecutionMode: "none",
				},
			},
			want: &model.RollbackSagaTransactionOutput{
				Success:             true,
				Message:             "事务正在补偿中（幂等性），回滚已启动",
				SagaId:              "test-saga-compensating-idempotent",
				NewStatus:           consts.SagaStatusCompensating,
				IsRollbackCompleted: false,
				FailReason:          "原始补偿失败原因",
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 3, consts.SagaStatusCompensating)
				// 创建不同补偿状态的步骤
				createTestSagaStepWithCompensationStatus(t, sagaID, "step1", "service1", 1, consts.CompensationStatusCompleted)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step2", "service2", 2, consts.CompensationStatusPending)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step3", "service3", 3, consts.CompensationStatusPending)
				// 设置失败原因
				dao.SagaTransactions.Ctx(context.Background()).Where("saga_id", sagaID).Update(g.Map{
					"fail_reason": "原始补偿失败原因",
				})
			},
		},
		{
			name: "场景5：Auto模式不自动执行回滚",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.RollbackSagaTransactionInput{
					SagaId:        "test-saga-auto-no-exec",
					FailReason:    "业务逻辑错误",
					FailedStep:    "step2",
					ExecutionMode: "none", // 不自动执行
				},
			},
			want: &model.RollbackSagaTransactionOutput{
				Success:             true,
				Message:             "回滚已启动，等待外部执行补偿操作",
				SagaId:              "test-saga-auto-no-exec",
				NewStatus:           consts.SagaStatusCompensating,
				IsRollbackCompleted: false,
				FailReason:          "业务逻辑错误",
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 2, consts.SagaStatusRunning)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step1", "service1", 1, consts.CompensationStatusUninitialized)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step2", "service2", 2, consts.CompensationStatusUninitialized)
			},
		},
		{
			name: "场景6：Auto模式自动执行回滚",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.RollbackSagaTransactionInput{
					SagaId:        "test-saga-auto-exec",
					FailReason:    "支付服务异常",
					FailedStep:    "ProcessPayment",
					ExecutionMode: "sync", // 同步执行
				},
			},
			want: &model.RollbackSagaTransactionOutput{
				Success:             true,
				Message:             "回滚流程已执行",
				SagaId:              "test-saga-auto-exec",
				NewStatus:           consts.SagaStatusFailed,
				IsRollbackCompleted: true,
				FailReason:          "支付服务异常",
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 3, consts.SagaStatusRunning)
				createTestSagaStepWithCompensationStatus(t, sagaID, "CreateOrder", "order-service", 1, consts.CompensationStatusUninitialized)
				createTestSagaStepWithCompensationStatus(t, sagaID, "ProcessPayment", "payment-service", 2, consts.CompensationStatusUninitialized)
				createTestSagaStepWithCompensationStatus(t, sagaID, "SendNotification", "notification-service", 3, consts.CompensationStatusUninitialized)
			},
		},
		{
			name: "场景6：Manual模式不自动执行回滚",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.RollbackSagaTransactionInput{
					SagaId:        "test-saga-manual-no-exec",
					FailReason:    "库存不足",
					FailedStep:    "ReserveInventory",
					ExecutionMode: "none",
				},
			},
			want: &model.RollbackSagaTransactionOutput{
				Success:             true,
				Message:             "回滚已启动，等待外部执行补偿操作",
				SagaId:              "test-saga-manual-no-exec",
				NewStatus:           consts.SagaStatusCompensating,
				IsRollbackCompleted: false,
				FailReason:          "库存不足",
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				templates := `[
					{"step_index":1,"service":"order-service","action":"CreateOrder"},
					{"step_index":2,"service":"payment-service","action":"ProcessPayment"},
					{"step_index":3,"service":"inventory-service","action":"ReserveInventory"}
				]`
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeManual, templates, 3, consts.SagaStatusRunning)
				createTestSagaStepWithCompensationStatus(t, sagaID, "CreateOrder", "order-service", 1, consts.CompensationStatusUninitialized)
				createTestSagaStepWithCompensationStatus(t, sagaID, "ProcessPayment", "payment-service", 2, consts.CompensationStatusUninitialized)
				createTestSagaStepWithCompensationStatus(t, sagaID, "ReserveInventory", "inventory-service", 3, consts.CompensationStatusUninitialized)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试数据
			tt.setupDB(tt.args.input.SagaId)
			defer cleanupTestData(t, tt.args.input.SagaId)

			got, err := tt.s.RollbackSagaTransaction(tt.args.ctx, tt.args.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("SagaTransactionsService.RollbackSagaTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil && got != nil && tt.want != nil {
				// 验证关键字段
				if got.Success != tt.want.Success {
					t.Errorf("Success = %v, want %v", got.Success, tt.want.Success)
				}
				if got.SagaId != tt.want.SagaId {
					t.Errorf("SagaId = %v, want %v", got.SagaId, tt.want.SagaId)
				}
				if got.NewStatus != tt.want.NewStatus {
					t.Errorf("NewStatus = %v, want %v", got.NewStatus, tt.want.NewStatus)
				}
				if got.IsRollbackCompleted != tt.want.IsRollbackCompleted {
					t.Errorf("IsRollbackCompleted = %v, want %v", got.IsRollbackCompleted, tt.want.IsRollbackCompleted)
				}
				if got.FailReason != tt.want.FailReason {
					t.Errorf("FailReason = %v, want %v", got.FailReason, tt.want.FailReason)
				}

				t.Logf("✅ 回滚测试通过: %s", tt.name)
				t.Logf("   SagaId: %s", got.SagaId)
				t.Logf("   新状态: %s", got.NewStatus)
				t.Logf("   回滚是否完成: %t", got.IsRollbackCompleted)
			}
		})
	}
}

// TestSagaTransactionsService_CheckRollbackStatus 测试检查回滚状态
func TestSagaTransactionsService_CheckRollbackStatus(t *testing.T) {
	type args struct {
		ctx   context.Context
		input *model.CheckRollbackStatusInput
	}
	tests := []struct {
		name    string
		s       *SagaTransactionsService
		args    args
		want    *model.CheckRollbackStatusOutput
		wantErr bool
		setupDB func(sagaId string) // 设置测试数据的函数
	}{
		{
			name: "场景1：检查运行中事务的回滚状态",
			s:    NewSagaTransactions(),
			args: args{
				ctx:   context.Background(),
				input: &model.CheckRollbackStatusInput{SagaId: "test-saga-running"},
			},
			want: &model.CheckRollbackStatusOutput{
				Success:                    true,
				Message:                    "回滚状态检查完成",
				SagaId:                     "test-saga-running",
				CurrentStatus:              consts.SagaStatusRunning,
				TotalStepsToRoll:           2,
				CompletedCompensations:     0,
				UninitializedCompensations: 2,
				FailedCompensations:        0,
				PendingCompensations:       0,
				IsRollbackCompleted:        false,
				IsRollbackFailed:           false,
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 2, consts.SagaStatusRunning)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step1", "service1", 1, consts.CompensationStatusUninitialized)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step2", "service2", 2, consts.CompensationStatusUninitialized)
			},
		},
		{
			name: "场景2：检查补偿中事务的状态",
			s:    NewSagaTransactions(),
			args: args{
				ctx:   context.Background(),
				input: &model.CheckRollbackStatusInput{SagaId: "test-saga-compensating"},
			},
			want: &model.CheckRollbackStatusOutput{
				Success:                    true,
				Message:                    "回滚状态检查完成",
				SagaId:                     "test-saga-compensating",
				CurrentStatus:              consts.SagaStatusCompensating,
				TotalStepsToRoll:           3,
				CompletedCompensations:     1, // 一个已完成补偿
				FailedCompensations:        0,
				PendingCompensations:       2, // 两个待补偿
				UninitializedCompensations: 0,
				IsRollbackCompleted:        false,
				IsRollbackFailed:           false,
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 3, consts.SagaStatusCompensating)
				// 创建步骤并设置不同的补偿状态
				createTestSagaStepWithCompensationStatus(t, sagaID, "step1", "service1", 1, consts.CompensationStatusCompleted)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step2", "service2", 2, consts.CompensationStatusPending)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step3", "service3", 3, consts.CompensationStatusPending)
			},
		},
		{
			name: "场景3：检查回滚完成的状态",
			s:    NewSagaTransactions(),
			args: args{
				ctx:   context.Background(),
				input: &model.CheckRollbackStatusInput{SagaId: "test-saga-rollback-completed"},
			},
			want: &model.CheckRollbackStatusOutput{
				Success:                true,
				Message:                "回滚状态检查完成",
				SagaId:                 "test-saga-rollback-completed",
				CurrentStatus:          consts.SagaStatusFailed, // 应该自动更新为failed
				TotalStepsToRoll:       2,
				CompletedCompensations: 2, // 所有补偿都完成了
				FailedCompensations:    0,
				PendingCompensations:   0,
				IsRollbackCompleted:    true,
				IsRollbackFailed:       false,
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 2, consts.SagaStatusFailed)
				// 创建所有步骤并设置为补偿完成
				createTestSagaStepWithCompensationStatus(t, sagaID, "step1", "service1", 1, consts.CompensationStatusCompleted)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step2", "service2", 2, consts.CompensationStatusCompleted)
			},
		},
		{
			name: "场景4：检查回滚失败的状态",
			s:    NewSagaTransactions(),
			args: args{
				ctx:   context.Background(),
				input: &model.CheckRollbackStatusInput{SagaId: "test-saga-rollback-failed"},
			},
			want: &model.CheckRollbackStatusOutput{
				Success:                true,
				Message:                "回滚状态检查完成",
				SagaId:                 "test-saga-rollback-failed",
				CurrentStatus:          consts.SagaStatusCompensating,
				TotalStepsToRoll:       3,
				CompletedCompensations: 1,
				FailedCompensations:    1, // 一个补偿失败
				PendingCompensations:   1,
				IsRollbackCompleted:    false,
				IsRollbackFailed:       true, // 回滚是否失败
			},
			wantErr: false,
			setupDB: func(sagaID string) {
				createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 3, consts.SagaStatusCompensating)
				// 创建步骤并设置不同的补偿状态
				createTestSagaStepWithCompensationStatus(t, sagaID, "step1", "service1", 1, consts.CompensationStatusCompleted)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step2", "service2", 2, consts.CompensationStatusFailed)
				createTestSagaStepWithCompensationStatus(t, sagaID, "step3", "service3", 3, consts.CompensationStatusPending)
			},
		},
		{
			name: "场景5：事务不存在",
			s:    NewSagaTransactions(),
			args: args{
				ctx:   context.Background(),
				input: &model.CheckRollbackStatusInput{SagaId: "non-existent-saga"},
			},
			want:    nil,
			wantErr: true,
			setupDB: func(sagaId string) {
				// 不需要设置数据，因为我们要测试不存在的情况
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试数据
			tt.setupDB(tt.args.input.SagaId)
			defer cleanupTestData(t, tt.args.input.SagaId)

			got, err := tt.s.CheckRollbackStatus(tt.args.ctx, tt.args.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("SagaTransactionsService.CheckRollbackStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err == nil && got != nil && tt.want != nil {
				// 验证关键字段
				if got.Success != tt.want.Success {
					t.Errorf("Success = %v, want %v", got.Success, tt.want.Success)
				}
				if got.SagaId != tt.want.SagaId {
					t.Errorf("SagaId = %v, want %v", got.SagaId, tt.want.SagaId)
				}
				if got.CurrentStatus != tt.want.CurrentStatus {
					t.Errorf("CurrentStatus = %v, want %v", got.CurrentStatus, tt.want.CurrentStatus)
				}
				if got.TotalStepsToRoll != tt.want.TotalStepsToRoll {
					t.Errorf("TotalStepsToRoll = %v, want %v", got.TotalStepsToRoll, tt.want.TotalStepsToRoll)
				}
				if got.CompletedCompensations != tt.want.CompletedCompensations {
					t.Errorf("CompletedCompensations = %v, want %v", got.CompletedCompensations, tt.want.CompletedCompensations)
				}
				if got.FailedCompensations != tt.want.FailedCompensations {
					t.Errorf("FailedCompensations = %v, want %v", got.FailedCompensations, tt.want.FailedCompensations)
				}
				if got.PendingCompensations != tt.want.PendingCompensations {
					t.Errorf("PendingCompensations = %v, want %v", got.PendingCompensations, tt.want.PendingCompensations)
				}
				if got.IsRollbackCompleted != tt.want.IsRollbackCompleted {
					t.Errorf("IsRollbackCompleted = %v, want %v", got.IsRollbackCompleted, tt.want.IsRollbackCompleted)
				}
				if got.IsRollbackFailed != tt.want.IsRollbackFailed {
					t.Errorf("IsRollbackFailed = %v, want %v", got.IsRollbackFailed, tt.want.IsRollbackFailed)
				}

				t.Logf("✅ 回滚状态检查测试通过: %s", tt.name)
			}
		})
	}
}
