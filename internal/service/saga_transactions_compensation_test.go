package service

import (
	"context"
	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"testing"
)

// TestRollbackOnlyCompensatesSuccessfulSteps 测试回滚只补偿成功的步骤
func TestRollbackOnlyCompensatesSuccessfulSteps(t *testing.T) {
	service := NewSagaTransactions()
	ctx := context.Background()
	sagaID := "test-saga-rollback-selective"

	// 清理测试数据
	defer cleanupTestData(t, sagaID)

	// 1. 创建Saga事务
	createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 3, consts.SagaStatusRunning)

	// 2. 创建3个步骤，其中一个失败
	createTestSagaStepWithCompensationStatus(t, sagaID, "CreateOrder", "order-service", 1, consts.CompensationStatusUninitialized)
	createTestSagaStepWithCompensationStatus(t, sagaID, "ProcessPayment", "payment-service", 2, consts.CompensationStatusUninitialized)
	createTestSagaStepWithCompensationStatus(t, sagaID, "SendNotification", "notification-service", 3, consts.CompensationStatusUninitialized)

	// 3. 执行回滚
	rollbackInput := &model.RollbackSagaTransactionInput{
		SagaId:        sagaID,
		FailReason:    "支付失败",
		FailedStep:    "ProcessPayment",
		ExecutionMode: "none", // 不自动执行补偿
	}

	result, err := service.RollbackSagaTransaction(ctx, rollbackInput)
	if err != nil {
		t.Fatalf("回滚失败: %v", err)
	}

	if !result.Success {
		t.Errorf("期望回滚成功，但返回失败: %s", result.Message)
	}

	// 4. 验证事务状态已更新为compensating
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询事务失败: %v", err)
	}

	if sagaTransaction.SagaStatus != consts.SagaStatusCompensating {
		t.Errorf("事务状态应该是%s，实际是%s", consts.SagaStatusCompensating, sagaTransaction.SagaStatus)
	}

	if sagaTransaction.FailReason != "支付失败" {
		t.Errorf("失败原因应该是'支付失败'，实际是'%s'", sagaTransaction.FailReason)
	}

	// 5. 验证步骤的补偿状态已更新为pending
	steps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询步骤失败: %v", err)
	}

	if len(steps) != 3 {
		t.Fatalf("期望3个步骤，实际%d个", len(steps))
	}

	// 验证所有步骤的补偿状态都已更新为pending
	for _, step := range steps {
		if step.CompensationStatus != consts.CompensationStatusPending {
			t.Errorf("步骤%s的补偿状态应该是%s，实际是%s", step.Action, consts.CompensationStatusPending, step.CompensationStatus)
		}
	}

	t.Log("✅ 回滚选择性补偿测试通过")
}

// TestBatchUpdateCompensationStatus 测试批量更新补偿状态功能
func TestBatchUpdateCompensationStatus(t *testing.T) {
	service := NewSagaTransactions()
	ctx := context.Background()
	sagaID := "test-saga-batch-update"

	// 清理测试数据
	defer cleanupTestData(t, sagaID)

	// 1. 创建Saga事务
	createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 5, consts.SagaStatusRunning)

	// 2. 创建5个成功完成的步骤（补偿状态为uninitialized）
	steps := []struct {
		action      string
		serviceName string
		stepIndex   int
	}{
		{"CreateOrder", "order-service", 1},
		{"ProcessPayment", "payment-service", 2},
		{"ReserveInventory", "inventory-service", 3},
		{"CreateShipment", "shipping-service", 4},
		{"SendNotification", "notification-service", 5},
	}

	for _, step := range steps {
		createTestSagaStepWithCompensationStatus(t, sagaID, step.action, step.serviceName, step.stepIndex, consts.CompensationStatusUninitialized)
	}

	// 3. 执行回滚（不自动执行补偿）
	rollbackInput := &model.RollbackSagaTransactionInput{
		SagaId:        sagaID,
		FailReason:    "库存不足",
		FailedStep:    "ReserveInventory",
		ExecutionMode: "none",
	}

	result, err := service.RollbackSagaTransaction(ctx, rollbackInput)
	if err != nil {
		t.Fatalf("回滚失败: %v", err)
	}

	if !result.Success {
		t.Errorf("期望回滚成功，但返回失败: %s", result.Message)
	}

	// 4. 验证所有步骤的补偿状态都已批量更新为pending
	updatedSteps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询更新后的步骤失败: %v", err)
	}

	if len(updatedSteps) != 5 {
		t.Fatalf("期望5个步骤，实际%d个", len(updatedSteps))
	}

	for _, step := range updatedSteps {
		if step.CompensationStatus != consts.CompensationStatusPending {
			t.Errorf("步骤%s的补偿状态应该是%s，实际是%s", step.Action, consts.CompensationStatusPending, step.CompensationStatus)
		}
	}

	// 5. 验证事务状态
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询事务失败: %v", err)
	}

	if sagaTransaction.SagaStatus != consts.SagaStatusCompensating {
		t.Errorf("事务状态应该是%s，实际是%s", consts.SagaStatusCompensating, sagaTransaction.SagaStatus)
	}

	t.Log("✅ 批量更新补偿状态测试通过")
}

// TestFromCompensationStatusExternalInput 测试FromCompensationStatus从外部传入的功能
func TestFromCompensationStatusExternalInput(t *testing.T) {
	service := NewSagaTransactions()
	ctx := context.Background()
	sagaID := "test-saga-external-status"

	// 预先清理可能存在的测试数据
	cleanupTestData(t, sagaID)
	cleanupTestData(t, sagaID+"-default")
	cleanupTestData(t, sagaID+"-complete")

	// 清理测试数据
	defer func() {
		cleanupTestData(t, sagaID)
		cleanupTestData(t, sagaID+"-default")
		cleanupTestData(t, sagaID+"-complete")
	}()

	// 测试场景1：测试默认值（空字符串应该默认为uninitialized）
	t.Run("测试默认值处理", func(t *testing.T) {
		// 重新创建一个新的事务进行测试
		testSagaID := sagaID + "-default"
		createRealTestSagaTransaction(testSagaID, consts.StepIndexModeAuto, "", 2, consts.SagaStatusRunning)
		createTestSagaStepWithCompensationStatus(t, testSagaID, "DefaultStep1", "service1", 1, consts.CompensationStatusUninitialized)
		createTestSagaStepWithCompensationStatus(t, testSagaID, "DefaultStep2", "service2", 2, consts.CompensationStatusUninitialized)

		rollbackOutput, err := service.RollbackSagaTransaction(ctx, &model.RollbackSagaTransactionInput{
			SagaId:     testSagaID,
			FailReason: "测试默认值",
		})

		if err != nil {
			t.Fatalf("开始回滚失败: %v", err)
		}

		if !rollbackOutput.Success {
			t.Errorf("回滚启动应该成功")
		}

		// 验证所有uninitialized状态的步骤都被更新为pending
		pendingCountAfter, err := dao.SagaSteps.CountCompensationsByStatus(ctx, testSagaID, consts.CompensationStatusPending)
		if err != nil {
			t.Fatalf("统计pending状态失败: %v", err)
		}
		if pendingCountAfter != 2 {
			t.Errorf("pending状态步骤数量错误: got %d, want 2", pendingCountAfter)
		}

		uninitializedCountAfter, err := dao.SagaSteps.CountCompensationsByStatus(ctx, testSagaID, consts.CompensationStatusUninitialized)
		if err != nil {
			t.Fatalf("统计uninitialized状态失败: %v", err)
		}
		if uninitializedCountAfter != 0 {
			t.Errorf("uninitialized状态步骤数量错误: got %d, want 0", uninitializedCountAfter)
		}
	})

	// 测试场景2：通过RollbackSagaTransaction测试参数传递
	t.Run("通过RollbackSagaTransaction测试参数传递", func(t *testing.T) {
		// 重新创建一个新的事务进行测试
		testSagaID := sagaID + "-complete"
		createRealTestSagaTransaction(testSagaID, consts.StepIndexModeAuto, "", 2, consts.SagaStatusRunning)
		createTestSagaStepWithCompensationStatus(t, testSagaID, "CompleteStep1", "service1", 1, consts.CompensationStatusUninitialized)
		createTestSagaStepWithCompensationStatus(t, testSagaID, "CompleteStep2", "service2", 2, consts.CompensationStatusUninitialized)

		rollbackOutput, err := service.RollbackSagaTransaction(ctx, &model.RollbackSagaTransactionInput{
			SagaId:        testSagaID,
			FailReason:    "测试完整的参数传递",
			ExecutionMode: "none",
		})

		if err != nil {
			t.Fatalf("回滚事务失败: %v", err)
		}

		if !rollbackOutput.Success {
			t.Errorf("回滚应该成功")
		}
	})

	t.Logf("✅ FromCompensationStatus外部传入测试通过")
	t.Logf("   验证了默认值处理和显式参数传递")
	t.Logf("   确保了参数从RollbackSagaTransaction正确传递到StartRollback")
}

// TestFindStepsForRollbackWithDifferentStatus 测试FindStepsForRollback接受不同状态参数的功能
func TestFindStepsForRollbackWithDifferentStatus(t *testing.T) {
	ctx := context.Background()
	sagaID := "test-saga-different-status"

	// 清理测试数据
	defer cleanupTestData(t, sagaID)

	// 1. 创建Saga事务
	createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 4, consts.SagaStatusRunning)

	// 2. 创建不同状态的步骤
	createTestSagaStepWithCompensationStatus(t, sagaID, "Step1", "service1", 1, consts.CompensationStatusUninitialized)
	createTestSagaStepWithCompensationStatus(t, sagaID, "Step2", "service2", 2, consts.CompensationStatusPending)
	createTestSagaStepWithCompensationStatus(t, sagaID, "Step3", "service3", 3, consts.CompensationStatusRunning)
	createTestSagaStepWithCompensationStatus(t, sagaID, "Step4", "service4", 4, consts.CompensationStatusFailed)

	// 测试场景1：查询uninitialized状态的步骤
	t.Run("查询uninitialized状态", func(t *testing.T) {
		steps, err := dao.SagaSteps.FindStepsForRollback(ctx, sagaID, consts.CompensationStatusUninitialized)
		if err != nil {
			t.Fatalf("查询uninitialized状态步骤失败: %v", err)
		}
		if len(steps) != 1 {
			t.Errorf("uninitialized状态步骤数量错误: got %d, want 1", len(steps))
		}
		if len(steps) > 0 && steps[0].Action != "Step1" {
			t.Errorf("uninitialized状态步骤错误: got %s, want Step1", steps[0].Action)
		}
	})

	// 测试场景2：查询pending状态的步骤
	t.Run("查询pending状态", func(t *testing.T) {
		steps, err := dao.SagaSteps.FindStepsForRollback(ctx, sagaID, consts.CompensationStatusPending)
		if err != nil {
			t.Fatalf("查询pending状态步骤失败: %v", err)
		}
		if len(steps) != 1 {
			t.Errorf("pending状态步骤数量错误: got %d, want 1", len(steps))
		}
		if len(steps) > 0 && steps[0].Action != "Step2" {
			t.Errorf("pending状态步骤错误: got %s, want Step2", steps[0].Action)
		}
	})

	// 测试场景3：查询running状态的步骤
	t.Run("查询running状态", func(t *testing.T) {
		steps, err := dao.SagaSteps.FindStepsForRollback(ctx, sagaID, consts.CompensationStatusRunning)
		if err != nil {
			t.Fatalf("查询running状态步骤失败: %v", err)
		}
		if len(steps) != 1 {
			t.Errorf("running状态步骤数量错误: got %d, want 1", len(steps))
		}
		if len(steps) > 0 && steps[0].Action != "Step3" {
			t.Errorf("running状态步骤错误: got %s, want Step3", steps[0].Action)
		}
	})

	// 测试场景4：查询failed状态的步骤
	t.Run("查询failed状态", func(t *testing.T) {
		steps, err := dao.SagaSteps.FindStepsForRollback(ctx, sagaID, consts.CompensationStatusFailed)
		if err != nil {
			t.Fatalf("查询failed状态步骤失败: %v", err)
		}
		if len(steps) != 1 {
			t.Errorf("failed状态步骤数量错误: got %d, want 1", len(steps))
		}
		if len(steps) > 0 && steps[0].Action != "Step4" {
			t.Errorf("failed状态步骤错误: got %s, want Step4", steps[0].Action)
		}
	})

	// 测试场景5：查询不存在的状态
	t.Run("查询不存在的状态", func(t *testing.T) {
		steps, err := dao.SagaSteps.FindStepsForRollback(ctx, sagaID, "nonexistent")
		if err != nil {
			t.Fatalf("查询不存在状态步骤失败: %v", err)
		}
		if len(steps) != 0 {
			t.Errorf("不存在状态步骤数量错误: got %d, want 0", len(steps))
		}
	})

	t.Logf("✅ FindStepsForRollback不同状态参数测试通过")
	t.Logf("   验证了可以查询不同补偿状态的步骤")
	t.Logf("   验证了步骤按step_index逆序排列")
	t.Logf("   提高了方法的灵活性和可扩展性")
}
