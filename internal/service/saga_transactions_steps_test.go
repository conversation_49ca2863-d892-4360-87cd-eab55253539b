package service

import (
	"context"
	"saga/internal/consts"
	"saga/internal/model/entity"
	"testing"
)

// TestSagaTransactionsService_checkAllStepsCompleted 测试检查所有步骤是否完成的方法
func TestSagaTransactionsService_checkAllStepsCompleted(t *testing.T) {
	type args struct {
		ctx             context.Context
		sagaTransaction *entity.SagaTransactions
	}
	tests := []struct {
		name              string
		args              args
		setupSteps        func(t *testing.T, sagaId string) // 创建测试步骤数据
		want              bool                              // 是否可以提交
		wantCompleted     int                               // 已完成步骤数
		wantExpected      int                               // 预期步骤数
		wantErr           bool                              // 是否有错误
		expectedErrSubstr string                            // 期望错误信息子串
	}{
		// ==========================================================
		// Auto 模式测试
		// ==========================================================
		{
			name: "Auto模式 - 成功：已完成步骤数等于预期步骤数",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-auto-1",
					StepIndexMode: consts.StepIndexModeAuto,
					CurStepIndex:  3, // 预期有3个步骤
				},
			},
			setupSteps: func(t *testing.T, sagaID string) {
				// 创建3个步骤记录
				createTestSagaStep(t, sagaID, "step1", "service1", 1)
				createTestSagaStep(t, sagaID, "step2", "service2", 2)
				createTestSagaStep(t, sagaID, "step3", "service3", 3)
			},
			want:          true,
			wantCompleted: 3,
			wantExpected:  3,
			wantErr:       false,
		},
		{
			name: "Auto模式 - 失败：已完成步骤数少于预期步骤数",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-auto-2",
					StepIndexMode: consts.StepIndexModeAuto,
					CurStepIndex:  3, // 预期有3个步骤
				},
			},
			setupSteps: func(t *testing.T, sagaID string) {
				// 只创建2个步骤记录
				createTestSagaStep(t, sagaID, "step1", "service1", 1)
				createTestSagaStep(t, sagaID, "step2", "service2", 2)
			},
			want:          false,
			wantCompleted: 2,
			wantExpected:  3,
			wantErr:       false,
		},
		{
			name: "Auto模式 - 失败：预期步骤数为0",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-auto-3",
					StepIndexMode: consts.StepIndexModeAuto,
					CurStepIndex:  0, // 预期有0个步骤
				},
			},
			setupSteps: func(t *testing.T, sagaID string) {
				// 不创建任何步骤记录
			},
			want:          false,
			wantCompleted: 0,
			wantExpected:  0,
			wantErr:       false,
		},
		{
			name: "Auto模式 - 失败：已完成步骤数多于预期步骤数",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-auto-4",
					StepIndexMode: consts.StepIndexModeAuto,
					CurStepIndex:  2, // 预期有2个步骤
				},
			},
			setupSteps: func(t *testing.T, sagaID string) {
				// 创建3个步骤记录
				createTestSagaStep(t, sagaID, "step1", "service1", 1)
				createTestSagaStep(t, sagaID, "step2", "service2", 2)
				createTestSagaStep(t, sagaID, "step3", "service3", 3)
			},
			want:          false,
			wantCompleted: 3,
			wantExpected:  2,
			wantErr:       false,
		},
		// ==========================================================
		// Manual 模式测试
		// ==========================================================
		{
			name: "Manual模式 - 成功：所有模板步骤都已上报",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-manual-1",
					StepIndexMode: consts.StepIndexModeManual,
					StepTemplates: `[
						{"step_index": 1, "service": "user-service", "action": "lock_user"},
						{"step_index": 2, "service": "stock-service", "action": "reserve_stock"},
						{"step_index": 3, "service": "order-service", "action": "create_order"}
					]`,
				},
			},
			setupSteps: func(t *testing.T, sagaID string) {
				// 按照模板创建步骤记录
				createTestSagaStep(t, sagaID, "lock_user", "user-service", 1)
				createTestSagaStep(t, sagaID, "reserve_stock", "stock-service", 2)
				createTestSagaStep(t, sagaID, "create_order", "order-service", 3)
			},
			want:          true,
			wantCompleted: 3,
			wantExpected:  3,
			wantErr:       false,
		},
		{
			name: "Manual模式 - 失败：有模板步骤未上报",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-manual-2",
					StepIndexMode: consts.StepIndexModeManual,
					StepTemplates: `[
						{"step_index": 1, "service": "user-service", "action": "lock_user"},
						{"step_index": 2, "service": "stock-service", "action": "reserve_stock"},
						{"step_index": 3, "service": "order-service", "action": "create_order"}
					]`,
				},
			},
			setupSteps: func(t *testing.T, sagaID string) {
				// 只创建前两个步骤记录，第三个步骤未上报
				createTestSagaStep(t, sagaID, "lock_user", "user-service", 1)
				createTestSagaStep(t, sagaID, "reserve_stock", "stock-service", 2)
				// 缺少 create_order 步骤
			},
			want:          false,
			wantCompleted: 2,
			wantExpected:  3,
			wantErr:       false,
		},
		{
			name: "Manual模式 - 特殊情况：步骤模板为空，降级为Auto模式",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-manual-3",
					StepIndexMode: consts.StepIndexModeManual,
					StepTemplates: "", // 空模板，降级为Auto模式
					CurStepIndex:  2,
				},
			},
			setupSteps: func(t *testing.T, sagaID string) {
				// 创建2个步骤记录
				createTestSagaStep(t, sagaID, "step1", "service1", 1)
				createTestSagaStep(t, sagaID, "step2", "service2", 2)
			},
			want:          true,
			wantCompleted: 2,
			wantExpected:  2,
			wantErr:       false,
		},
		{
			name: "Manual模式 - 特殊情况：步骤模板为空数组，降级为Auto模式",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-manual-4",
					StepIndexMode: consts.StepIndexModeManual,
					StepTemplates: "[]", // 空数组，降级为Auto模式
					CurStepIndex:  1,
				},
			},
			setupSteps: func(t *testing.T, sagaID string) {
				// 创建1个步骤记录
				createTestSagaStep(t, sagaID, "step1", "service1", 1)
			},
			want:          true,
			wantCompleted: 1,
			wantExpected:  1,
			wantErr:       false,
		},
		{
			name: "Manual模式 - 错误：步骤模板JSON解析失败",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-manual-5",
					StepIndexMode: consts.StepIndexModeManual,
					StepTemplates: `invalid json format`, // 无效的JSON格式
				},
			},
			setupSteps: func(t *testing.T, sagaId string) {
				// 不创建步骤记录，因为会在解析JSON时失败
			},
			want:              false,
			wantCompleted:     0,
			wantExpected:      0,
			wantErr:           true,
			expectedErrSubstr: "解析步骤模板失败",
		},
		// ==========================================================
		// 未知模式测试
		// ==========================================================
		{
			name: "未知模式 - 错误：未知的StepIndexMode",
			args: args{
				ctx: context.Background(),
				sagaTransaction: &entity.SagaTransactions{
					SagaId:        "test-saga-unknown-1",
					StepIndexMode: "unknown_mode", // 未知的模式
				},
			},
			setupSteps: func(t *testing.T, sagaId string) {
				// 不创建步骤记录，因为会在检查模式时失败
			},
			want:              false,
			wantCompleted:     0,
			wantExpected:      0,
			wantErr:           true,
			expectedErrSubstr: "未知的 StepIndexMode",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 清理之前的测试数据
			cleanupTestData(t, tt.args.sagaTransaction.SagaId)

			// 设置测试数据
			if tt.setupSteps != nil {
				tt.setupSteps(t, tt.args.sagaTransaction.SagaId)
			}

			// 执行测试
			s := NewSagaTransactions()
			canCommit, completedSteps, expectedSteps, err := s.checkAllStepsCompleted(tt.args.ctx, tt.args.sagaTransaction)

			// 验证错误
			if tt.wantErr {
				if err == nil {
					t.Errorf("checkAllStepsCompleted() 期望有错误但没有返回错误")
					return
				}
				if tt.expectedErrSubstr != "" && !contains(err.Error(), tt.expectedErrSubstr) {
					t.Errorf("checkAllStepsCompleted() 错误信息不匹配, 期望包含 %q, 实际 %q", tt.expectedErrSubstr, err.Error())
				}
				return
			}

			// 验证成功情况
			if err != nil {
				t.Errorf("checkAllStepsCompleted() 不期望错误但返回了错误: %v", err)
				return
			}

			if canCommit != tt.want {
				t.Errorf("checkAllStepsCompleted() canCommit = %v, want %v", canCommit, tt.want)
			}

			if completedSteps != tt.wantCompleted {
				t.Errorf("checkAllStepsCompleted() completedSteps = %v, want %v", completedSteps, tt.wantCompleted)
			}

			if expectedSteps != tt.wantExpected {
				t.Errorf("checkAllStepsCompleted() expectedSteps = %v, want %v", expectedSteps, tt.wantExpected)
			}

			// 清理测试数据
			cleanupTestData(t, tt.args.sagaTransaction.SagaId)
		})
	}
}
