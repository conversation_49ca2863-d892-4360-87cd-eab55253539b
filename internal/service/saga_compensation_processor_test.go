package service

import (
	"context"
	"testing"
	"time"

	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/guid"

	"saga/internal/model/entity"
)

// TestNewCompensationProcessorService 测试创建补偿处理服务实例
func TestNewCompensationProcessorService(t *testing.T) {
	service := NewCompensationProcessorService()

	// 验证默认配置
	if service.taskTimeout != 5*time.Minute {
		t.Errorf("Expected taskTimeout to be 5 minutes, got %v", service.taskTimeout)
	}

	if service.recoveryInterval != 30*time.Second {
		t.Errorf("Expected recoveryInterval to be 30 seconds, got %v", service.recoveryInterval)
	}

	if service.maxConcurrentTasks != 10 {
		t.Errorf("Expected maxConcurrentTasks to be 10, got %d", service.maxConcurrentTasks)
	}

	if service.isRecoveryRunning {
		t.Error("Expected isRecoveryRunning to be false")
	}

	if service.sagaService == nil {
		t.Error("Expected sagaService to be initialized")
	}

	if service.stopChan == nil {
		t.Error("Expected stopChan to be initialized")
	}

	if service.taskSemaphore == nil {
		t.Error("Expected taskSemaphore to be initialized")
	}

	if cap(service.taskSemaphore) != 10 {
		t.Errorf("Expected taskSemaphore capacity to be 10, got %d", cap(service.taskSemaphore))
	}

	t.Logf("✅ 补偿处理服务实例创建成功，默认配置正确")
}

// TestSetConfig 测试设置配置
func TestSetConfig(t *testing.T) {
	service := NewCompensationProcessorService()

	// 设置新配置
	newTaskTimeout := 10 * time.Minute
	newProcessingInterval := 60 * time.Second
	newMaxConcurrentTasks := 20

	service.SetConfig(newTaskTimeout, newProcessingInterval, newMaxConcurrentTasks)

	// 验证配置是否正确设置
	if service.taskTimeout != newTaskTimeout {
		t.Errorf("Expected taskTimeout to be %v, got %v", newTaskTimeout, service.taskTimeout)
	}

	if service.recoveryInterval != newProcessingInterval {
		t.Errorf("Expected recoveryInterval to be %v, got %v", newProcessingInterval, service.recoveryInterval)
	}

	if service.maxConcurrentTasks != newMaxConcurrentTasks {
		t.Errorf("Expected maxConcurrentTasks to be %d, got %d", newMaxConcurrentTasks, service.maxConcurrentTasks)
	}

	// 验证信号量容量是否更新
	if cap(service.taskSemaphore) != newMaxConcurrentTasks {
		t.Errorf("Expected taskSemaphore capacity to be %d, got %d", newMaxConcurrentTasks, cap(service.taskSemaphore))
	}

	t.Logf("✅ 配置设置成功")
}

// TestStartProcessing 测试启动补偿处理服务
func TestStartProcessing(t *testing.T) {
	service := NewCompensationProcessorService()
	ctx := context.Background()

	// 测试正常启动
	err := service.StartProcessing(ctx)
	if err != nil {
		t.Errorf("Expected no error when starting processing, got: %v", err)
	}

	// 验证状态
	if !service.isRecoveryRunning {
		t.Error("Expected isRecoveryRunning to be true after starting")
	}

	// 测试重复启动
	err = service.StartProcessing(ctx)
	if err == nil {
		t.Error("Expected error when starting processing that is already running")
	}

	expectedErrorMsg := "补偿处理服务已在运行中"
	if err.Error() != expectedErrorMsg {
		t.Errorf("Expected error message '%s', got '%s'", expectedErrorMsg, err.Error())
	}

	// 清理
	service.StopProcessing(ctx)
}

// TestStopProcessing 测试停止补偿处理服务
func TestStopProcessing(t *testing.T) {
	service := NewCompensationProcessorService()
	ctx := context.Background()

	// 测试停止未启动的服务
	err := service.StopProcessing(ctx)
	if err == nil {
		t.Error("Expected error when stopping processing that is not running")
	}

	expectedErrorMsg := "补偿处理服务未在运行"
	if err.Error() != expectedErrorMsg {
		t.Errorf("Expected error message '%s', got '%s'", expectedErrorMsg, err.Error())
	}

	// 启动服务
	err = service.StartProcessing(ctx)
	if err != nil {
		t.Fatalf("Failed to start processing service: %v", err)
	}

	// 验证服务正在运行
	if !service.isRecoveryRunning {
		t.Error("Service should be running")
	}

	// 测试正常停止
	err = service.StopProcessing(ctx)
	if err != nil {
		t.Errorf("Expected no error when stopping processing, got: %v", err)
	}

	// 验证状态
	if service.isRecoveryRunning {
		t.Error("Expected isRecoveryRunning to be false after stopping")
	}
}

// TestGroupTasksBySagaID 测试按 SagaId 分组任务
func TestGroupTasksBySagaID(t *testing.T) {
	service := NewCompensationProcessorService()

	// 创建测试任务
	tasks := []*entity.SagaSteps{
		{SagaId: "saga-001", StepId: "step-001"},
		{SagaId: "saga-001", StepId: "step-002"},
		{SagaId: "saga-002", StepId: "step-003"},
		{SagaId: "saga-002", StepId: "step-004"},
		{SagaId: "saga-003", StepId: "step-005"},
	}

	// 执行分组
	groups := service.groupTasksBySagaID(tasks)

	// 验证分组结果
	if len(groups) != 3 {
		t.Errorf("Expected 3 groups, got %d", len(groups))
	}

	if len(groups["saga-001"]) != 2 {
		t.Errorf("Expected 2 tasks in saga-001, got %d", len(groups["saga-001"]))
	}

	if len(groups["saga-002"]) != 2 {
		t.Errorf("Expected 2 tasks in saga-002, got %d", len(groups["saga-002"]))
	}

	if len(groups["saga-003"]) != 1 {
		t.Errorf("Expected 1 task in saga-003, got %d", len(groups["saga-003"]))
	}

	t.Logf("✅ 任务分组功能正常")
}

// TestSortTasksByStepIndexDesc 测试按步骤索引逆序排序
func TestSortTasksByStepIndexDesc(t *testing.T) {
	service := NewCompensationProcessorService()

	// 创建测试任务（乱序）
	tasks := []*entity.SagaSteps{
		{StepId: "step-002", StepIndex: 2},
		{StepId: "step-001", StepIndex: 1},
		{StepId: "step-004", StepIndex: 4},
		{StepId: "step-003", StepIndex: 3},
	}

	// 执行排序
	sortedTasks := service.sortTasksByStepIndexDesc(tasks)

	// 验证排序结果（应该是逆序：4, 3, 2, 1）
	expectedOrder := []int{4, 3, 2, 1}
	for i, task := range sortedTasks {
		if task.StepIndex != expectedOrder[i] {
			t.Errorf("Expected step index %d at position %d, got %d", expectedOrder[i], i, task.StepIndex)
		}
	}

	// 验证原始数组未被修改
	if tasks[0].StepIndex != 2 {
		t.Error("Original array should not be modified")
	}

	t.Logf("✅ 任务排序功能正常")
}

// TestExecuteCompensationTaskMock 测试执行补偿任务（模拟）
func TestExecuteCompensationTaskMock(t *testing.T) {
	service := NewCompensationProcessorService()
	ctx := context.Background()

	// 创建测试任务
	task := &entity.SagaSteps{
		StepId:              guid.S(),
		SagaId:              "test-saga-001",
		StepIndex:           1,
		Action:              "CreateOrder",
		ServiceName:         "order-service",
		CompensateEndpoint:  "http://localhost:8080/compensate",
		CompensationContext: `{"orderId": "12345"}`,
		CompensationStatus:  "pending",
		CreatedAt:           gtime.Now(),
		UpdatedAt:           gtime.Now(),
	}

	// 执行补偿任务（会失败，因为没有真实的 Saga 事务）
	success := service.executeCompensationTask(ctx, task)

	// 在单元测试环境中，由于没有真实的数据库数据，预期会失败
	if success {
		t.Log("Task execution succeeded (unexpected in unit test)")
	} else {
		t.Log("Task execution failed (expected in unit test)")
	}
}

// TestProcessSagaCompensationsLogic 测试 Saga 补偿处理逻辑
func TestProcessSagaCompensationsLogic(t *testing.T) {
	service := NewCompensationProcessorService()
	ctx := context.Background()

	sagaID := "test-saga-logic"

	// 创建测试任务
	tasks := []*entity.SagaSteps{
		{
			StepId:             "step-001",
			SagaId:             sagaID,
			StepIndex:          1,
			Action:             "CreateOrder",
			CompensationStatus: "pending",
		},
		{
			StepId:             "step-002",
			SagaId:             sagaID,
			StepIndex:          2,
			Action:             "ProcessPayment",
			CompensationStatus: "pending",
		},
		{
			StepId:             "step-003",
			SagaId:             sagaID,
			StepIndex:          3,
			Action:             "SendNotification",
			CompensationStatus: "running", // 这个任务会被处理
		},
	}

	// 执行补偿处理
	successCount, errorCount := service.processSagaCompensations(ctx, sagaID, tasks)

	// 在单元测试环境中，由于没有真实的 Saga 事务，所有任务都会失败
	if successCount > 0 {
		t.Logf("Saga transaction found, tasks processed successfully: %d", successCount)
	} else {
		t.Logf("Saga transaction not found, all tasks counted as errors (expected in unit test)")
	}

	totalTasks := len(tasks)
	if successCount+errorCount != totalTasks {
		t.Errorf("Expected total processed tasks to be %d, got %d", totalTasks, successCount+errorCount)
	}

	t.Logf("Processing result: success=%d, error=%d, total=%d", successCount, errorCount, totalTasks)
}

// TestGetProcessingStatus 测试获取处理服务状态
func TestGetProcessingStatus(t *testing.T) {
	service := NewCompensationProcessorService()
	ctx := context.Background()

	// 设置自定义配置
	taskTimeout := 10 * time.Minute
	processingInterval := 60 * time.Second
	maxConcurrentTasks := 20
	service.SetConfig(taskTimeout, processingInterval, maxConcurrentTasks)

	// 测试未运行状态
	status, err := service.GetProcessingStatus(ctx)
	if err != nil {
		// 可能因为数据库连接失败，记录日志但不终止测试
		t.Logf("GetProcessingStatus failed (expected in unit test): %v", err)
		return
	}

	// 验证配置信息
	if status.IsRecoveryRunning {
		t.Error("Expected IsRecoveryRunning to be false")
	}

	if status.TaskTimeout != taskTimeout {
		t.Errorf("Expected TaskTimeout to be %v, got %v", taskTimeout, status.TaskTimeout)
	}

	if status.RecoveryInterval != processingInterval {
		t.Errorf("Expected RecoveryInterval to be %v, got %v", processingInterval, status.RecoveryInterval)
	}

	if status.MaxConcurrentTasks != maxConcurrentTasks {
		t.Errorf("Expected MaxConcurrentTasks to be %d, got %d", maxConcurrentTasks, status.MaxConcurrentTasks)
	}

	t.Logf("✅ 处理服务状态获取成功")
}
