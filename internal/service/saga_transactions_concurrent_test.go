package service

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"saga/internal/model/entity"

	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/guid"
)

// TestConcurrentReportCompensationAndRollback 测试 ReportCompensation 和 RollbackSagaTransaction 的并发安全性
func TestConcurrentReportCompensationAndRollback(t *testing.T) {
	service := NewSagaTransactions()
	ctx := context.Background()

	// 创建唯一的测试ID
	testID := guid.S()
	sagaID := "test-concurrent-" + testID[0:8]

	// 清理测试数据
	defer func() {
		// 删除步骤记录
		dao.SagaSteps.Ctx(ctx).Where("saga_id", sagaID).Delete()
		// 删除事务记录
		dao.SagaTransactions.Ctx(ctx).Where("saga_id", sagaID).Delete()
	}()

	// 创建测试 Saga 事务
	sagaTransaction := &entity.SagaTransactions{
		SagaId:        sagaID,
		Name:          "并发测试事务",
		SagaStatus:    consts.SagaStatusRunning,
		StepIndexMode: consts.StepIndexModeAuto,
		CurStepIndex:  0,
		CreatedAt:     gtime.Now(),
		UpdatedAt:     gtime.Now(),
	}

	_, err := dao.SagaTransactions.Ctx(ctx).Insert(sagaTransaction)
	if err != nil {
		t.Fatalf("创建测试 Saga 事务失败: %v", err)
	}

	// 并发测试参数
	const goroutineCount = 5
	var wg sync.WaitGroup
	var mutex sync.Mutex

	// 结果收集
	reportResults := make([]error, goroutineCount)
	rollbackResults := make([]error, goroutineCount)

	t.Logf("开始并发测试: %d 个 ReportCompensation 和 %d 个 RollbackSagaTransaction", goroutineCount, goroutineCount)

	// 启动 ReportCompensation 的 goroutine
	for i := 0; i < goroutineCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// 稍微延迟一下，让所有goroutine同时开始
			time.Sleep(time.Millisecond * 10)

			_, err := service.ReportCompensation(ctx, &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              "test_action_" + guid.S()[0:8],
				ServiceName:         "test-service",
				ContextData:         `{"test": "data"}`,
				CompensationContext: `{"compensate": "data"}`,
				CompensateEndpoint:  "http://test-service/compensate",
			})

			mutex.Lock()
			reportResults[index] = err
			mutex.Unlock()
		}(i)
	}

	// 启动 RollbackSagaTransaction 的 goroutine
	for i := 0; i < goroutineCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// 稍微延迟一下，让所有goroutine同时开始
			time.Sleep(time.Millisecond * 10)

			_, err := service.RollbackSagaTransaction(ctx, &model.RollbackSagaTransactionInput{
				SagaId:        sagaID,
				FailReason:    "并发测试回滚",
				ExecutionMode: consts.CompensationExecutionModeNone,
			})

			mutex.Lock()
			rollbackResults[index] = err
			mutex.Unlock()
		}(i)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 分析结果
	reportSuccessCount := 0
	rollbackSuccessCount := 0

	for i := 0; i < goroutineCount; i++ {
		if reportResults[i] == nil {
			reportSuccessCount++
		} else {
			t.Logf("ReportCompensation %d 失败: %v", i, reportResults[i])
		}

		if rollbackResults[i] == nil {
			rollbackSuccessCount++
		} else {
			t.Logf("RollbackSagaTransaction %d 失败: %v", i, rollbackResults[i])
		}
	}

	t.Logf("并发测试结果:")
	t.Logf("  ReportCompensation 成功: %d/%d", reportSuccessCount, goroutineCount)
	t.Logf("  RollbackSagaTransaction 成功: %d/%d", rollbackSuccessCount, goroutineCount)

	// 验证数据一致性
	finalSagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询最终 Saga 状态失败: %v", err)
	}

	if finalSagaTransaction != nil {
		t.Logf("最终 Saga 状态: %s", finalSagaTransaction.SagaStatus)

		// 验证状态的合理性
		validStates := []string{consts.SagaStatusRunning, consts.SagaStatusCompensating, consts.SagaStatusFailed}
		isValidState := false
		for _, state := range validStates {
			if finalSagaTransaction.SagaStatus == state {
				isValidState = true
				break
			}
		}

		if !isValidState {
			t.Errorf("最终状态不合理: %s", finalSagaTransaction.SagaStatus)
		}
	}

	// 查询步骤记录
	steps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询步骤记录失败: %v", err)
	}

	t.Logf("最终步骤记录数: %d", len(steps))

	// 验证步骤索引的唯一性
	if len(steps) > 0 {
		stepIndexMap := make(map[int]bool)
		for _, step := range steps {
			if stepIndexMap[step.StepIndex] {
				t.Errorf("发现重复的步骤索引: %d", step.StepIndex)
			}
			stepIndexMap[step.StepIndex] = true
		}
	}

	// 如果有回滚成功的情况，验证状态是否正确
	if rollbackSuccessCount > 0 {
		if finalSagaTransaction.SagaStatus != consts.SagaStatusCompensating && finalSagaTransaction.SagaStatus != consts.SagaStatusFailed {
			t.Logf("注意: 回滚成功但状态不是 compensating 或 failed: %s", finalSagaTransaction.SagaStatus)
		}
	}

	t.Logf("✅ 并发安全测试完成")
}

// TestReportCompensationDuringRollback 测试在回滚过程中上报补偿信息
func TestReportCompensationDuringRollback(t *testing.T) {
	service := NewSagaTransactions()
	ctx := context.Background()

	// 创建唯一的测试ID
	testID := guid.S()
	timestamp := time.Now().UnixNano()
	sagaID := fmt.Sprintf("test-rollback-report-%s-%d", testID[0:8], timestamp%100000)

	// 预先清理可能存在的测试数据
	cleanupTestData(t, sagaID)

	// 清理测试数据
	defer cleanupTestData(t, sagaID)

	// 创建测试 Saga 事务
	sagaTransaction := &entity.SagaTransactions{
		SagaId:        sagaID,
		Name:          "回滚期间上报测试",
		SagaStatus:    consts.SagaStatusRunning,
		StepIndexMode: consts.StepIndexModeAuto,
		CurStepIndex:  1,
		CreatedAt:     gtime.Now(),
		UpdatedAt:     gtime.Now(),
	}

	_, err := dao.SagaTransactions.Ctx(ctx).Insert(sagaTransaction)
	if err != nil {
		t.Fatalf("创建测试 Saga 事务失败: %v", err)
	}

	// 创建一个已完成的步骤
	step := &entity.SagaSteps{
		StepId:              guid.S(),
		SagaId:              sagaID,
		Action:              "initial_action",
		StepIndex:           1,
		ServiceName:         "initial-service",
		ContextData:         `{"initial": "data"}`,
		CompensationContext: `{"compensate": "data"}`,
		CompensateEndpoint:  "http://initial-service/compensate",
		CompensationStatus:  consts.CompensationStatusUninitialized,
		CreatedAt:           gtime.Now(),
		UpdatedAt:           gtime.Now(),
	}

	_, err = dao.SagaSteps.Ctx(ctx).Insert(step)
	if err != nil {
		t.Fatalf("创建测试步骤失败: %v", err)
	}

	var wg sync.WaitGroup
	var rollbackErr error
	var reportErr error
	var reportResult *model.ReportCompensationOutput

	// 启动回滚操作
	wg.Add(1)
	go func() {
		defer wg.Done()
		_, rollbackErr = service.RollbackSagaTransaction(ctx, &model.RollbackSagaTransactionInput{
			SagaId:        sagaID,
			FailReason:    "测试回滚",
			ExecutionMode: consts.CompensationExecutionModeNone,
		})
	}()

	// 稍微延迟后尝试上报补偿信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		time.Sleep(time.Millisecond * 50) // 让回滚先开始

		reportResult, reportErr = service.ReportCompensation(ctx, &model.ReportCompensationInput{
			SagaId:              sagaID,
			Action:              "late_action",
			ServiceName:         "late-service",
			ContextData:         `{"late": "data"}`,
			CompensationContext: `{"compensate": "data"}`,
			CompensateEndpoint:  "http://late-service/compensate",
		})
	}()

	wg.Wait()

	t.Logf("回滚结果: %v", rollbackErr)
	t.Logf("上报结果: %v", reportErr)
	if reportResult != nil {
		t.Logf("上报返回: Success=%v, Message=%s", reportResult.Success, reportResult.Message)
	}

	// 验证最终状态
	finalSaga, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询最终状态失败: %v", err)
	}

	t.Logf("最终状态: %s", finalSaga.SagaStatus)

	// 查询步骤记录
	finalSteps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询步骤记录失败: %v", err)
	}

	t.Logf("最终步骤数: %d", len(finalSteps))

	// 验证业务逻辑的正确性
	if finalSaga.SagaStatus == consts.SagaStatusCompensating {
		// 如果状态是补偿中，那么延迟的上报应该被拒绝
		if reportErr != nil {
			t.Errorf("延迟的上报不应该返回错误，但实际返回了错误: %v", reportErr)
		}
		if reportResult == nil {
			t.Errorf("期望得到上报结果，但实际为 nil")
		} else if reportResult.Success {
			t.Errorf("期望延迟的上报被拒绝 (Success=false)，但实际成功了 (Success=true)")
		} else {
			t.Logf("✅ 延迟上报被正确拒绝: %s", reportResult.Message)
		}
	}

	t.Logf("✅ 回滚期间上报测试完成")
}

// TestReportCompensation_ConcurrentAutoMode 测试 Auto 模式下的并发处理
func TestReportCompensation_ConcurrentAutoMode(t *testing.T) {
	service := NewSagaTransactions()

	// 创建测试用的 Saga 事务（Auto 模式）
	sagaID := createTestSagaTransaction(t, consts.StepIndexModeAuto)
	defer cleanupTestData(t, sagaID)

	// 并发测试参数
	concurrentCount := 10
	var wg sync.WaitGroup
	results := make([]error, concurrentCount)

	// 启动多个并发请求
	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			input := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              fmt.Sprintf("test_action_%d", index),
				ServiceName:         fmt.Sprintf("test-service-%d", index),
				ContextData:         `{"test": "data"}`,
				CompensationContext: `{"compensate": "data"}`,
				CompensateEndpoint:  fmt.Sprintf("http://test-service-%d/compensate", index),
			}

			_, err := service.ReportCompensation(context.Background(), input)
			results[index] = err
		}(i)
	}

	// 等待所有并发请求完成
	wg.Wait()

	// 验证结果
	successCount := 0
	for i, err := range results {
		if err == nil {
			successCount++
		} else {
			t.Logf("请求 %d 失败: %v", i, err)
		}
	}

	// 所有请求都应该成功
	if successCount != concurrentCount {
		t.Errorf("期望 %d 个请求成功，实际成功 %d 个", concurrentCount, successCount)
	}

	// 验证数据库中的步骤记录
	steps, err := dao.SagaSteps.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询步骤记录失败: %v", err)
	}

	if len(steps) != concurrentCount {
		t.Errorf("期望 %d 个步骤记录，实际 %d 个", concurrentCount, len(steps))
	}

	// 验证步骤索引的唯一性和连续性
	stepIndexMap := make(map[int]bool)
	for _, step := range steps {
		if stepIndexMap[step.StepIndex] {
			t.Errorf("发现重复的步骤索引: %d", step.StepIndex)
		}
		stepIndexMap[step.StepIndex] = true
	}

	// 验证步骤索引应该是 1 到 concurrentCount
	for i := 1; i <= concurrentCount; i++ {
		if !stepIndexMap[i] {
			t.Errorf("缺少步骤索引: %d", i)
		}
	}

	t.Logf("并发测试成功: %d 个请求全部成功处理", concurrentCount)
}

// TestReportCompensation_HighConcurrencyStress 高并发压力测试
func TestReportCompensation_HighConcurrencyStress(t *testing.T) {
	service := NewSagaTransactions()

	// 创建测试用的 Saga 事务（Auto 模式）
	sagaID := createTestSagaTransaction(t, consts.StepIndexModeAuto)
	defer cleanupTestData(t, sagaID)

	// 高并发测试参数
	concurrentCount := 50
	var wg sync.WaitGroup
	results := make([]error, concurrentCount)
	startTime := time.Now()

	// 启动多个并发请求
	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			input := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              fmt.Sprintf("stress_action_%d", index),
				ServiceName:         fmt.Sprintf("stress-service-%d", index),
				ContextData:         `{"test": "data"}`,
				CompensationContext: `{"compensate": "data"}`,
				CompensateEndpoint:  fmt.Sprintf("http://stress-service-%d/compensate", index),
			}

			_, err := service.ReportCompensation(context.Background(), input)
			results[index] = err
		}(i)
	}

	// 等待所有并发请求完成
	wg.Wait()
	duration := time.Since(startTime)

	// 统计结果
	successCount := 0
	retryCount := 0
	for i, err := range results {
		if err == nil {
			successCount++
		} else {
			t.Logf("请求 %d 失败: %v", i, err)
			retryCount++
		}
	}

	// 计算性能指标
	throughput := float64(successCount) / duration.Seconds()

	t.Logf("高并发压力测试结果:")
	t.Logf("  并发数: %d", concurrentCount)
	t.Logf("  成功数: %d", successCount)
	t.Logf("  失败数: %d", concurrentCount-successCount)
	t.Logf("  重试相关失败: %d", retryCount)
	t.Logf("  总耗时: %v", duration)
	t.Logf("  吞吐量: %.2f 请求/秒", throughput)

	// 验证成功率应该合理（至少30%，在高并发场景下仍存在竞争条件）
	successRate := float64(successCount) / float64(concurrentCount)
	if successRate < 0.3 {
		t.Errorf("成功率过低: %.2f%%，期望至少30%%", successRate*100)
	}

	// 验证数据库中的步骤记录
	steps, err := dao.SagaSteps.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询步骤记录失败: %v", err)
	}

	if len(steps) != successCount {
		t.Errorf("期望 %d 个步骤记录，实际 %d 个", successCount, len(steps))
	}
}

// TestReportCompensation_ManualModeNoConflict 测试 Manual 模式下无冲突
func TestReportCompensation_ManualModeNoConflict(t *testing.T) {
	service := NewSagaTransactions()

	// 创建测试用的 Saga 事务（Manual 模式）
	sagaID := createTestSagaTransactionWithTemplates(t, consts.StepIndexModeManual)
	defer cleanupTestData(t, sagaID)

	// 并发测试参数
	concurrentCount := 5
	var wg sync.WaitGroup
	results := make([]error, concurrentCount)

	// 预定义的步骤模板
	actions := []string{"lock_user", "reserve_stock", "create_order", "process_payment", "send_notification"}
	services := []string{"user-service", "stock-service", "order-service", "payment-service", "notification-service"}

	// 启动多个并发请求
	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			input := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              actions[index],
				ServiceName:         services[index],
				ContextData:         `{"test": "data"}`,
				CompensationContext: `{"compensate": "data"}`,
				CompensateEndpoint:  fmt.Sprintf("http://%s/compensate", services[index]),
			}

			_, err := service.ReportCompensation(context.Background(), input)
			results[index] = err
		}(i)
	}

	// 等待所有并发请求完成
	wg.Wait()

	// 验证结果
	successCount := 0
	for i, err := range results {
		if err == nil {
			successCount++
		} else {
			t.Logf("请求 %d 失败: %v", i, err)
		}
	}

	// 所有请求都应该成功（Manual 模式下不应该有冲突）
	if successCount != concurrentCount {
		t.Errorf("期望 %d 个请求成功，实际成功 %d 个", concurrentCount, successCount)
	}

	t.Logf("Manual 模式并发测试成功: %d 个请求全部成功处理", concurrentCount)
}

// TestReportCompensation_ConcurrentSameActionAutoMode 测试 Auto 模式下相同 action 的并发处理
func TestReportCompensation_ConcurrentSameActionAutoMode(t *testing.T) {
	service := NewSagaTransactions()

	// 创建测试用的 Saga 事务（Auto 模式）
	sagaID := createTestSagaTransaction(t, consts.StepIndexModeAuto)
	defer cleanupTestData(t, sagaID)

	// 并发测试参数
	concurrentCount := 20
	var wg sync.WaitGroup
	results := make([]error, concurrentCount)
	successResults := make([]*model.ReportCompensationOutput, concurrentCount)

	// 相同的 action 和 service_name
	const actionName = "create_order"
	const serviceName = "order-service"

	startTime := time.Now()

	// 启动多个并发请求，使用相同的 action 和 service_name
	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			input := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              actionName,  // 相同的 action
				ServiceName:         serviceName, // 相同的 service_name
				ContextData:         fmt.Sprintf(`{"orderId": "order-%d", "attempt": %d}`, index, index),
				CompensationContext: fmt.Sprintf(`{"orderId": "order-%d", "reason": "rollback"}`, index),
				CompensateEndpoint:  "/order/compensate",
			}

			result, err := service.ReportCompensation(context.Background(), input)
			results[index] = err
			successResults[index] = result
		}(i)
	}

	// 等待所有并发请求完成
	wg.Wait()

	duration := time.Since(startTime)

	// 验证结果
	successCount := 0
	for i, err := range results {
		if err == nil {
			successCount++
		} else {
			t.Logf("请求 %d 失败: %v", i, err)
		}
	}

	t.Logf("并发测试完成: 总请求数=%d, 成功数=%d, 失败数=%d, 耗时=%v",
		concurrentCount, successCount, concurrentCount-successCount, duration)

	// 验证数据库中的步骤记录
	steps, err := dao.SagaSteps.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询步骤记录失败: %v", err)
	}

	// 关键验证1：相同的 action 和 service_name 应该只有1条记录
	sameActionSteps := 0
	for _, step := range steps {
		if step.Action == actionName && step.ServiceName == serviceName {
			sameActionSteps++
		}
	}

	t.Logf("数据库验证: 总步骤数=%d, 相同action步骤数=%d", len(steps), sameActionSteps)

	if sameActionSteps != 1 {
		t.Errorf("预期相同action只有1条记录，但实际有%d条记录", sameActionSteps)

		// 打印所有相关记录的详细信息
		for i, step := range steps {
			if step.Action == actionName && step.ServiceName == serviceName {
				t.Logf("记录%d: StepId=%s, StepIndex=%d, Action=%s, ServiceName=%s, ContextData=%s",
					i+1, step.StepId, step.StepIndex, step.Action, step.ServiceName, step.ContextData)
			}
		}
	}

	// 关键验证2：检查 step_index 的连续性
	stepIndexes := make([]int, 0)
	for _, step := range steps {
		stepIndexes = append(stepIndexes, step.StepIndex)
	}

	// 验证是否有重复的 step_index
	stepIndexMap := make(map[int]int)
	for _, index := range stepIndexes {
		stepIndexMap[index]++
	}

	duplicateIndexes := make([]int, 0)
	for index, count := range stepIndexMap {
		if count > 1 {
			duplicateIndexes = append(duplicateIndexes, index)
		}
	}

	if len(duplicateIndexes) > 0 {
		t.Errorf("发现重复的step_index: %v", duplicateIndexes)
	}

	// 验证 step_index 的连续性
	expectedIndex := 1
	for _, step := range steps {
		if step.StepIndex != expectedIndex {
			t.Errorf("step_index不连续：期望%d，实际%d", expectedIndex, step.StepIndex)
		}
		expectedIndex++
	}

	// 验证 saga_transactions 表的状态
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询 saga 事务失败: %v", err)
	}

	t.Logf("Saga事务状态: CurStepIndex=%d, 期望值=%d", sagaTransaction.CurStepIndex, len(steps))

	if sagaTransaction.CurStepIndex != len(steps) {
		t.Errorf("CurStepIndex不正确：期望%d，实际%d", len(steps), sagaTransaction.CurStepIndex)
	}

	// 如果测试失败，打印详细的调试信息
	if t.Failed() {
		t.Log("=== 调试信息 ===")
		t.Logf("SagaId: %s", sagaID)
		t.Logf("Action: %s", actionName)
		t.Logf("ServiceName: %s", serviceName)
		t.Logf("并发数: %d", concurrentCount)
		t.Logf("成功请求数: %d", successCount)
		t.Logf("数据库记录数: %d", len(steps))
		t.Logf("相同action记录数: %d", sameActionSteps)
		t.Logf("当前CurStepIndex: %d", sagaTransaction.CurStepIndex)

		t.Log("所有步骤记录:")
		for i, step := range steps {
			t.Logf("  %d. StepId=%s, StepIndex=%d, Action=%s, ServiceName=%s",
				i+1, step.StepId, step.StepIndex, step.Action, step.ServiceName)
		}
	}
}

// TestReportCompensation_IdempotencyValidation 测试幂等性验证
func TestReportCompensation_IdempotencyValidation(t *testing.T) {
	service := NewSagaTransactions()

	// 创建测试用的 Saga 事务（Auto 模式）
	sagaID := createTestSagaTransaction(t, consts.StepIndexModeAuto)
	defer cleanupTestData(t, sagaID)

	const actionName = "idempotent_action"
	const serviceName = "idempotent-service"

	// 第一次调用
	input1 := &model.ReportCompensationInput{
		SagaId:              sagaID,
		Action:              actionName,
		ServiceName:         serviceName,
		ContextData:         `{"data": "first_call"}`,
		CompensationContext: `{"compensate": "first_call"}`,
		CompensateEndpoint:  "/idempotent/compensate",
	}

	result1, err := service.ReportCompensation(context.Background(), input1)
	if err != nil {
		t.Fatalf("第一次调用失败: %v", err)
	}

	if !result1.Success {
		t.Fatalf("第一次调用应该成功")
	}

	// 第二次调用 - 相同的 action 和 service_name，但不同的 context
	input2 := &model.ReportCompensationInput{
		SagaId:              sagaID,
		Action:              actionName,
		ServiceName:         serviceName,
		ContextData:         `{"data": "second_call"}`,
		CompensationContext: `{"compensate": "second_call"}`,
		CompensateEndpoint:  "/idempotent/compensate",
	}

	result2, err := service.ReportCompensation(context.Background(), input2)
	if err != nil {
		t.Fatalf("第二次调用失败: %v", err)
	}

	if !result2.Success {
		t.Fatalf("第二次调用应该成功")
	}

	// 验证数据库中只有一条记录
	steps, err := dao.SagaSteps.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询步骤记录失败: %v", err)
	}

	// 找到相同 action 和 service_name 的记录
	var targetSteps []*entity.SagaSteps
	for _, step := range steps {
		if step.Action == actionName && step.ServiceName == serviceName {
			targetSteps = append(targetSteps, step)
		}
	}

	if len(targetSteps) != 1 {
		t.Errorf("预期只有1条记录，但实际有%d条记录", len(targetSteps))
		for i, step := range targetSteps {
			t.Logf("记录%d: StepId=%s, StepIndex=%d, ContextData=%s",
				i+1, step.StepId, step.StepIndex, step.ContextData)
		}
	} else {
		// 验证记录的内容是否被正确更新
		finalStep := targetSteps[0]
		t.Logf("最终记录: StepId=%s, StepIndex=%d, ContextData=%s",
			finalStep.StepId, finalStep.StepIndex, finalStep.ContextData)

		// 验证最后的 ContextData 是否是第二次调用的内容
		if finalStep.ContextData != `{"data": "second_call"}` {
			t.Errorf("最终记录的ContextData不正确: 期望=%s, 实际=%s",
				`{"data": "second_call"}`, finalStep.ContextData)
		}
	}
}

// TestReportCompensation_ConcurrentWithDifferentServices 测试不同服务的并发处理
func TestReportCompensation_ConcurrentWithDifferentServices(t *testing.T) {
	service := NewSagaTransactions()

	// 创建测试用的 Saga 事务（Auto 模式）
	sagaID := createTestSagaTransaction(t, consts.StepIndexModeAuto)
	defer cleanupTestData(t, sagaID)

	// 并发测试参数
	concurrentCount := 10
	var wg sync.WaitGroup
	results := make([]error, concurrentCount)

	// 不同的服务和动作
	services := []string{"user-service", "order-service", "payment-service", "inventory-service", "notification-service"}
	actions := []string{"create_user", "create_order", "process_payment", "reserve_inventory", "send_notification"}

	startTime := time.Now()

	// 启动多个并发请求
	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			serviceIndex := index % len(services)
			actionIndex := index % len(actions)

			input := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              actions[actionIndex],
				ServiceName:         services[serviceIndex],
				ContextData:         fmt.Sprintf(`{"index": %d, "service": "%s", "action": "%s"}`, index, services[serviceIndex], actions[actionIndex]),
				CompensationContext: fmt.Sprintf(`{"compensate": "rollback", "index": %d}`, index),
				CompensateEndpoint:  fmt.Sprintf("http://%s/compensate", services[serviceIndex]),
			}

			_, err := service.ReportCompensation(context.Background(), input)
			results[index] = err
		}(i)
	}

	// 等待所有并发请求完成
	wg.Wait()

	duration := time.Since(startTime)

	// 验证结果
	successCount := 0
	for i, err := range results {
		if err == nil {
			successCount++
		} else {
			t.Logf("请求 %d 失败: %v", i, err)
		}
	}

	t.Logf("不同服务并发测试完成: 总请求数=%d, 成功数=%d, 失败数=%d, 耗时=%v",
		concurrentCount, successCount, concurrentCount-successCount, duration)

	// 验证数据库中的步骤记录
	steps, err := dao.SagaSteps.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询步骤记录失败: %v", err)
	}

	// 验证每个 action + service_name 组合只有一条记录
	actionServiceMap := make(map[string]int)
	for _, step := range steps {
		key := fmt.Sprintf("%s:%s", step.Action, step.ServiceName)
		actionServiceMap[key]++
	}

	for key, count := range actionServiceMap {
		if count > 1 {
			t.Errorf("组合 %s 有 %d 条记录，违反了幂等性", key, count)
		}
	}

	// 验证 step_index 的唯一性
	stepIndexMap := make(map[int]int)
	for _, step := range steps {
		stepIndexMap[step.StepIndex]++
	}

	for index, count := range stepIndexMap {
		if count > 1 {
			t.Errorf("step_index=%d 出现了%d次，违反了唯一性", index, count)
		}
	}

	t.Logf("最终验证: 总记录数=%d, 唯一action+service组合数=%d", len(steps), len(actionServiceMap))
}

// 创建测试用的 Saga 事务（Auto 模式）
func createTestSagaTransaction(t *testing.T, stepIndexMode string) string {
	sagaID := guid.S()

	sagaTransaction := &entity.SagaTransactions{
		SagaId:        sagaID,
		Name:          "test-saga",
		SagaStatus:    consts.SagaStatusPending,
		StepIndexMode: stepIndexMode,
		CurStepIndex:  0,
		CreatedAt:     gtime.Now(),
		UpdatedAt:     gtime.Now(),
	}

	_, err := dao.SagaTransactions.Insert(context.Background(), sagaTransaction)
	if err != nil {
		t.Fatalf("创建测试 Saga 事务失败: %v", err)
	}

	return sagaID
}

// 创建测试用的 Saga 事务（Manual 模式，包含模板）
func createTestSagaTransactionWithTemplates(t *testing.T, stepIndexMode string) string {
	sagaID := guid.S()

	templates := `[
		{"step_index": 1, "service": "user-service", "action": "lock_user"},
		{"step_index": 2, "service": "stock-service", "action": "reserve_stock"},
		{"step_index": 3, "service": "order-service", "action": "create_order"},
		{"step_index": 4, "service": "payment-service", "action": "process_payment"},
		{"step_index": 5, "service": "notification-service", "action": "send_notification"}
	]`

	sagaTransaction := &entity.SagaTransactions{
		SagaId:        sagaID,
		Name:          "test-saga-manual",
		SagaStatus:    consts.SagaStatusPending,
		StepIndexMode: stepIndexMode,
		StepTemplates: templates,
		CurStepIndex:  0,
		CreatedAt:     gtime.Now(),
		UpdatedAt:     gtime.Now(),
	}

	_, err := dao.SagaTransactions.Insert(context.Background(), sagaTransaction)
	if err != nil {
		t.Fatalf("创建测试 Saga 事务失败: %v", err)
	}

	return sagaID
}
