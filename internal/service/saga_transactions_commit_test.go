package service

import (
	"context"
	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"testing"
)

// 测试 CommitSagaTransaction 功能
func TestSagaTransactionsService_CommitSagaTransaction(t *testing.T) {
	type args struct {
		ctx   context.Context
		input *model.CommitSagaTransactionInput
	}
	tests := []struct {
		name    string
		s       *SagaTransactionsService
		args    args
		want    *model.CommitSagaTransactionOutput
		wantErr bool
		setupDB func() // 设置测试数据的函数
	}{
		{
			name: "场景1：分布式事务不存在",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.CommitSagaTransactionInput{
					SagaId: "non-existent-saga-id",
				},
			},
			want:    nil,
			wantErr: true, // 期望返回错误，因为事务不存在
			setupDB: func() {
				// 不需要设置数据，因为我们要测试不存在的情况
			},
		},
		{
			name: "场景2：事务状态不是 running",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.CommitSagaTransactionInput{
					SagaId: "test-saga-pending",
				},
			},
			want: &model.CommitSagaTransactionOutput{
				Success:   false,
				Message:   "无法提交事务，当前状态为: pending，只有 running 状态的事务才能提交",
				SagaId:    "test-saga-pending",
				NewStatus: "pending",
			},
			wantErr: false,
			setupDB: func() {
				// 清理之前的数据
				cleanupTestData(t, "test-saga-pending")
				// 创建一个状态为 pending 的事务
				createRealTestSagaTransaction("test-saga-pending", consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)
				t.Log("已创建状态为 pending 的事务")
			},
		},
		{
			name: "场景3：Auto 模式 - 步骤未完成",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.CommitSagaTransactionInput{
					SagaId: "test-saga-auto-incomplete",
				},
			},
			want: &model.CommitSagaTransactionOutput{
				Success:        false,
				Message:        "无法提交事务，还有步骤未完成，已完成: 2/3",
				SagaId:         "test-saga-auto-incomplete",
				CompletedSteps: 2,
				ExpectedSteps:  3,
				NewStatus:      "running",
			},
			wantErr: false,
			setupDB: func() {
				// 清理之前的数据
				cleanupTestData(t, "test-saga-auto-incomplete")
				// 创建一个 Auto 模式的事务，CurStepIndex=3，但只有 2 个步骤
				createRealTestSagaTransaction("test-saga-auto-incomplete", consts.StepIndexModeAuto, "", 3, consts.SagaStatusRunning)
				// 只创建2个步骤，但期望3个步骤
				createTestSagaStep(t, "test-saga-auto-incomplete", "step1", "service1", 1)
				createTestSagaStep(t, "test-saga-auto-incomplete", "step2", "service2", 2)
				t.Log("已创建 Auto 模式未完成的事务")
			},
		},
		{
			name: "场景4：Auto 模式 - 成功提交",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.CommitSagaTransactionInput{
					SagaId: "test-saga-auto-complete",
				},
			},
			want: &model.CommitSagaTransactionOutput{
				Success:        true,
				Message:        "分布式事务提交成功",
				SagaId:         "test-saga-auto-complete",
				CompletedSteps: 3,
				ExpectedSteps:  3,
				NewStatus:      "completed",
			},
			wantErr: false,
			setupDB: func() {
				// 清理之前的数据
				cleanupTestData(t, "test-saga-auto-complete")
				// 创建一个 Auto 模式的事务，CurStepIndex=3，且有 3 个步骤
				createRealTestSagaTransaction("test-saga-auto-complete", consts.StepIndexModeAuto, "", 3, consts.SagaStatusRunning)
				// 创建3个步骤，完全匹配期望
				createTestSagaStep(t, "test-saga-auto-complete", "step1", "service1", 1)
				createTestSagaStep(t, "test-saga-auto-complete", "step2", "service2", 2)
				createTestSagaStep(t, "test-saga-auto-complete", "step3", "service3", 3)
				t.Log("已创建 Auto 模式已完成的事务")
			},
		},
		{
			name: "场景5：Manual 模式 - 步骤未完成",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.CommitSagaTransactionInput{
					SagaId: "test-saga-manual-incomplete",
				},
			},
			want: &model.CommitSagaTransactionOutput{
				Success:        false,
				Message:        "无法提交事务，还有步骤未完成，已完成: 2/3",
				SagaId:         "test-saga-manual-incomplete",
				CompletedSteps: 2,
				ExpectedSteps:  3,
				NewStatus:      "running",
			},
			wantErr: false,
			setupDB: func() {
				// 清理之前的数据
				cleanupTestData(t, "test-saga-manual-incomplete")
				// 创建一个 Manual 模式的事务，有 3 个模板步骤，但只上报了 2 个
				stepTemplates := generateStepTemplates()
				createRealTestSagaTransaction("test-saga-manual-incomplete", consts.StepIndexModeManual, stepTemplates, 0, consts.SagaStatusRunning)
				// 只创建前两个步骤，缺少第三个
				createTestSagaStep(t, "test-saga-manual-incomplete", "lock_user", "user-service", 1)
				createTestSagaStep(t, "test-saga-manual-incomplete", "process_payment", "payment-service", 2)
				// 缺少 send_notification 步骤
				t.Log("已创建 Manual 模式未完成的事务")
			},
		},
		{
			name: "场景6：Manual 模式 - 成功提交",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.CommitSagaTransactionInput{
					SagaId: "test-saga-manual-complete",
				},
			},
			want: &model.CommitSagaTransactionOutput{
				Success:        true,
				Message:        "分布式事务提交成功",
				SagaId:         "test-saga-manual-complete",
				CompletedSteps: 3,
				ExpectedSteps:  3,
				NewStatus:      "completed",
			},
			wantErr: false,
			setupDB: func() {
				// 清理之前的数据
				cleanupTestData(t, "test-saga-manual-complete")
				// 创建一个 Manual 模式的事务，有 3 个模板步骤，且都已上报
				stepTemplates := generateStepTemplates()
				createRealTestSagaTransaction("test-saga-manual-complete", consts.StepIndexModeManual, stepTemplates, 0, consts.SagaStatusRunning)
				// 创建所有模板步骤
				createTestSagaStep(t, "test-saga-manual-complete", "lock_user", "user-service", 1)
				createTestSagaStep(t, "test-saga-manual-complete", "process_payment", "payment-service", 2)
				createTestSagaStep(t, "test-saga-manual-complete", "send_notification", "notification-service", 3)
				t.Log("已创建 Manual 模式已完成的事务")
			},
		},
		{
			name: "场景7：Manual 模式降级为 Auto 模式",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.CommitSagaTransactionInput{
					SagaId: "test-saga-manual-fallback",
				},
			},
			want: &model.CommitSagaTransactionOutput{
				Success:        true,
				Message:        "分布式事务提交成功",
				SagaId:         "test-saga-manual-fallback",
				CompletedSteps: 2,
				ExpectedSteps:  2,
				NewStatus:      "completed",
			},
			wantErr: false,
			setupDB: func() {
				// 清理之前的数据
				cleanupTestData(t, "test-saga-manual-fallback")
				// 创建一个 Manual 模式的事务，但 StepTemplates 为空，降级为 Auto 模式
				createRealTestSagaTransaction("test-saga-manual-fallback", consts.StepIndexModeManual, "", 2, consts.SagaStatusRunning)
				// 创建2个步骤，降级为Auto模式时应该成功
				createTestSagaStep(t, "test-saga-manual-fallback", "step1", "service1", 1)
				createTestSagaStep(t, "test-saga-manual-fallback", "step2", "service2", 2)
				t.Log("已创建 Manual 模式降级为 Auto 模式的事务")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试数据
			if tt.setupDB != nil {
				tt.setupDB()
			}

			// 延迟清理（确保测试完成后清理）
			defer func() {
				if tt.args.input != nil {
					cleanupTestData(t, tt.args.input.SagaId)
				}
			}()

			// 执行测试
			got, err := tt.s.CommitSagaTransaction(tt.args.ctx, tt.args.input)

			// 验证错误
			if (err != nil) != tt.wantErr {
				t.Errorf("SagaTransactionsService.CommitSagaTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 验证返回值（只验证关键字段）
			if got != nil && tt.want != nil {
				if got.Success != tt.want.Success {
					t.Errorf("SagaTransactionsService.CommitSagaTransaction() Success = %v, want %v", got.Success, tt.want.Success)
				}
				if got.SagaId != tt.want.SagaId {
					t.Errorf("SagaTransactionsService.CommitSagaTransaction() SagaId = %v, want %v", got.SagaId, tt.want.SagaId)
				}
				if got.NewStatus != tt.want.NewStatus {
					t.Errorf("SagaTransactionsService.CommitSagaTransaction() NewStatus = %v, want %v", got.NewStatus, tt.want.NewStatus)
				}
				if got.CompletedSteps != tt.want.CompletedSteps {
					t.Errorf("SagaTransactionsService.CommitSagaTransaction() CompletedSteps = %v, want %v", got.CompletedSteps, tt.want.CompletedSteps)
				}
				if got.ExpectedSteps != tt.want.ExpectedSteps {
					t.Errorf("SagaTransactionsService.CommitSagaTransaction() ExpectedSteps = %v, want %v", got.ExpectedSteps, tt.want.ExpectedSteps)
				}
			}

			// 额外验证：检查数据库状态
			if !tt.wantErr && got != nil && got.Success {
				// 验证 SagaTransactions 表中的状态是否正确更新为 completed
				verifySagaTransactionCommitted(t, tt.args.input.SagaId)
				t.Logf("测试成功：%s", tt.name)
			} else if !tt.wantErr && got != nil && !got.Success {
				// 验证失败的情况下状态没有被改变
				t.Logf("测试预期失败：%s - %s", tt.name, got.Message)
			}
		})
	}
}

// 测试辅助函数：创建完整的测试用例
func TestSagaTransactionsService_CommitSagaTransaction_Integration(t *testing.T) {
	// 这是一个集成测试示例，展示完整的 Commit 流程

	// 测试场景1：Auto 模式完整流程
	t.Run("Auto 模式完整流程", func(t *testing.T) {
		service := NewSagaTransactions()
		ctx := context.Background()
		sagaID := "test-saga-auto-integration"

		// 清理测试数据
		defer cleanupTestData(t, sagaID)

		// 1. 创建事务
		t.Log("创建 Auto 模式事务")
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 3, consts.SagaStatusRunning)

		// 2. 创建步骤记录
		t.Log("创建 3 个步骤记录")
		createTestSagaStep(t, sagaID, "step1", "service1", 1)
		createTestSagaStep(t, sagaID, "step2", "service2", 2)
		createTestSagaStep(t, sagaID, "step3", "service3", 3)

		// 3. 提交事务
		t.Log("提交事务")
		commitInput := &model.CommitSagaTransactionInput{
			SagaId: sagaID,
		}

		result, err := service.CommitSagaTransaction(ctx, commitInput)
		if err != nil {
			t.Fatalf("提交事务失败: %v", err)
		}

		if !result.Success {
			t.Errorf("期望提交成功，但返回失败: %s", result.Message)
		}

		// 4. 验证结果
		if result.CompletedSteps != 3 {
			t.Errorf("期望完成步骤数为3，实际为%d", result.CompletedSteps)
		}
		if result.ExpectedSteps != 3 {
			t.Errorf("期望预期步骤数为3，实际为%d", result.ExpectedSteps)
		}
		if result.NewStatus != consts.SagaStatusCompleted {
			t.Errorf("期望状态为%s，实际为%s", consts.SagaStatusCompleted, result.NewStatus)
		}

		// 5. 验证数据库状态
		verifySagaTransactionCommitted(t, sagaID)
		verifySagaStepsCompleted(t, sagaID, 3)

		t.Log("Auto 模式完整流程测试完成")
	})

	// 测试场景2：Manual 模式完整流程
	t.Run("Manual 模式完整流程", func(t *testing.T) {
		service := NewSagaTransactions()
		ctx := context.Background()
		sagaID := "test-saga-manual-integration"

		// 清理测试数据
		defer cleanupTestData(t, sagaID)

		// 1. 创建事务
		t.Log("创建 Manual 模式事务，包含 3 个步骤模板")
		stepTemplates := generateStepTemplates()
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeManual, stepTemplates, 0, consts.SagaStatusRunning)

		// 2. 按模板创建步骤记录
		t.Log("按模板创建所有步骤")
		createTestSagaStep(t, sagaID, "lock_user", "user-service", 1)
		createTestSagaStep(t, sagaID, "process_payment", "payment-service", 2)
		createTestSagaStep(t, sagaID, "send_notification", "notification-service", 3)

		// 3. 提交事务
		t.Log("提交事务")
		commitInput := &model.CommitSagaTransactionInput{
			SagaId: sagaID,
		}

		result, err := service.CommitSagaTransaction(ctx, commitInput)
		if err != nil {
			t.Fatalf("提交事务失败: %v", err)
		}

		if !result.Success {
			t.Errorf("期望提交成功，但返回失败: %s", result.Message)
		}

		// 4. 验证结果
		if result.CompletedSteps != 3 {
			t.Errorf("期望完成步骤数为3，实际为%d", result.CompletedSteps)
		}
		if result.ExpectedSteps != 3 {
			t.Errorf("期望预期步骤数为3，实际为%d", result.ExpectedSteps)
		}
		if result.NewStatus != consts.SagaStatusCompleted {
			t.Errorf("期望状态为%s，实际为%s", consts.SagaStatusCompleted, result.NewStatus)
		}

		// 5. 验证数据库状态
		verifySagaTransactionCommitted(t, sagaID)
		verifySagaStepsCompleted(t, sagaID, 3)

		t.Log("Manual 模式完整流程测试完成")
	})

	// 测试场景3：失败的提交（步骤不完整）
	t.Run("失败的提交（步骤不完整）", func(t *testing.T) {
		service := NewSagaTransactions()
		ctx := context.Background()
		sagaID := "test-saga-fail-integration"

		// 清理测试数据
		defer cleanupTestData(t, sagaID)

		// 1. 创建事务
		t.Log("创建 Auto 模式事务")
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 3, consts.SagaStatusRunning)

		// 2. 只创建部分步骤记录
		t.Log("只创建 2 个步骤记录（缺少第3个）")
		createTestSagaStep(t, sagaID, "step1", "service1", 1)
		createTestSagaStep(t, sagaID, "step2", "service2", 2)

		// 3. 提交事务
		t.Log("提交事务")
		commitInput := &model.CommitSagaTransactionInput{
			SagaId: sagaID,
		}

		result, err := service.CommitSagaTransaction(ctx, commitInput)
		if err != nil {
			t.Fatalf("提交事务失败: %v", err)
		}

		if result.Success {
			t.Errorf("期望提交失败，但返回成功")
		}

		// 4. 验证结果
		if result.CompletedSteps != 2 {
			t.Errorf("期望完成步骤数为2，实际为%d", result.CompletedSteps)
		}
		if result.ExpectedSteps != 3 {
			t.Errorf("期望预期步骤数为3，实际为%d", result.ExpectedSteps)
		}
		if result.NewStatus != consts.SagaStatusRunning {
			t.Errorf("期望状态为%s，实际为%s", consts.SagaStatusRunning, result.NewStatus)
		}

		// 5. 验证数据库状态（事务状态应该保持running）
		sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
		if err != nil {
			t.Fatalf("查询事务失败: %v", err)
		}
		if sagaTransaction.SagaStatus != consts.SagaStatusRunning {
			t.Errorf("事务状态不应该改变，期望%s，实际%s", consts.SagaStatusRunning, sagaTransaction.SagaStatus)
		}

		t.Log("失败的提交测试完成")
	})
}
