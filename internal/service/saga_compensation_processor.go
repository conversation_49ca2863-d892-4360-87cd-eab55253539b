package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"saga/internal/model/entity"
)

// CompensationProcessorService 补偿处理服务
// 负责处理所有未完成的补偿操作，包括中断恢复、超时重试等场景
type CompensationProcessorService struct {
	sagaService        *SagaTransactionsService
	taskTimeout        time.Duration // 任务超时时间
	recoveryInterval   time.Duration // 恢复检查间隔
	maxConcurrentTasks int           // 最大并发任务数
	isRecoveryRunning  bool          // 恢复进程是否运行中
	recoveryMutex      sync.RWMutex  // 恢复进程锁
	stopChan           chan struct{} // 停止信号通道
	taskSemaphore      chan struct{} // 任务信号量
}

// NewCompensationProcessorService 创建补偿处理服务实例
func NewCompensationProcessorService() *CompensationProcessorService {
	return &CompensationProcessorService{
		sagaService:        NewSagaTransactions(),
		taskTimeout:        5 * time.Minute,  // 默认5分钟超时
		recoveryInterval:   30 * time.Second, // 默认30秒检查一次
		maxConcurrentTasks: 10,               // 默认最大10个并发任务
		isRecoveryRunning:  false,
		stopChan:           make(chan struct{}),
		taskSemaphore:      make(chan struct{}, 10),
	}
}

// SetConfig 设置处理服务配置
func (s *CompensationProcessorService) SetConfig(taskTimeout, recoveryInterval time.Duration, maxConcurrentTasks int) {
	s.taskTimeout = taskTimeout
	s.recoveryInterval = recoveryInterval
	s.maxConcurrentTasks = maxConcurrentTasks
	s.taskSemaphore = make(chan struct{}, maxConcurrentTasks)
}

// StartProcessing 启动补偿处理服务
func (s *CompensationProcessorService) StartProcessing(ctx context.Context) error {
	s.recoveryMutex.Lock()
	defer s.recoveryMutex.Unlock()

	if s.isRecoveryRunning {
		return fmt.Errorf("补偿处理服务已在运行中")
	}

	s.isRecoveryRunning = true
	g.Log().Infof(ctx, "启动补偿处理服务: 任务超时=%v, 检查间隔=%v, 最大并发=%d",
		s.taskTimeout, s.recoveryInterval, s.maxConcurrentTasks)

	// 首次启动时立即执行一次处理
	go s.runProcessing(ctx)

	// 启动定时处理任务
	go s.startPeriodicProcessing(ctx)

	return nil
}

// StopProcessing 停止补偿处理服务
func (s *CompensationProcessorService) StopProcessing(ctx context.Context) error {
	s.recoveryMutex.Lock()
	defer s.recoveryMutex.Unlock()

	if !s.isRecoveryRunning {
		return fmt.Errorf("补偿处理服务未在运行")
	}

	s.isRecoveryRunning = false
	close(s.stopChan)
	g.Log().Infof(ctx, "补偿处理服务已停止")

	return nil
}

// startPeriodicProcessing 启动定时处理任务
func (s *CompensationProcessorService) startPeriodicProcessing(ctx context.Context) {
	ticker := time.NewTicker(s.recoveryInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			g.Log().Infof(ctx, "收到停止信号，退出定时处理任务")
			return
		case <-ticker.C:
			s.runProcessing(ctx)
		}
	}
}

// runProcessing 执行处理任务
func (s *CompensationProcessorService) runProcessing(ctx context.Context) {
	recoveryStartTime := gtime.Now()
	g.Log().Infof(ctx, "开始执行补偿处理任务")

	// 1. 重置超时的运行中任务
	resetCount, err := s.resetStaleRunningTasks(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "重置超时任务失败: %v", err)
	} else if resetCount > 0 {
		g.Log().Infof(ctx, "重置超时任务成功: %d 个", resetCount)
	}

	// 2. 获取所有未完成的补偿任务
	unfinishedTasks, err := dao.SagaSteps.FindUnfinishedCompensations(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "获取未完成补偿任务失败: %v", err)
		return
	}

	if len(unfinishedTasks) == 0 {
		g.Log().Infof(ctx, "没有发现未完成的补偿任务")
		return
	}

	g.Log().Infof(ctx, "发现 %d 个未完成的补偿任务", len(unfinishedTasks))

	// 3. 按 SagaId 分组处理任务
	sagaGroups := s.groupTasksBySagaID(unfinishedTasks)

	// 4. 并发处理每个 Saga 的补偿任务
	var wg sync.WaitGroup
	var countMutex sync.Mutex
	successCount := 0
	errorCount := 0

	for sagaID, tasks := range sagaGroups {
		wg.Add(1)
		go func(sagaID string, tasks []*entity.SagaSteps) {
			defer wg.Done()

			// 获取信号量
			s.taskSemaphore <- struct{}{}
			defer func() {
				<-s.taskSemaphore
			}()

			success, errors := s.processSagaCompensations(ctx, sagaID, tasks)

			countMutex.Lock()
			successCount += success
			errorCount += errors
			countMutex.Unlock()
		}(sagaID, tasks)
	}

	wg.Wait()

	processingDuration := gtime.Now().Sub(recoveryStartTime)
	g.Log().Infof(ctx, "补偿处理任务完成: 成功=%d, 失败=%d, 耗时=%v",
		successCount, errorCount, processingDuration)
}

// resetStaleRunningTasks 重置超时的运行中任务
func (s *CompensationProcessorService) resetStaleRunningTasks(ctx context.Context) (int64, error) {
	timeoutMinutes := int(s.taskTimeout.Minutes())
	return dao.SagaSteps.ResetStaleRunningCompensations(ctx, timeoutMinutes)
}

// groupTasksBySagaId 按 SagaId 分组任务
func (s *CompensationProcessorService) groupTasksBySagaID(tasks []*entity.SagaSteps) map[string][]*entity.SagaSteps {
	sagaGroups := make(map[string][]*entity.SagaSteps)
	for _, task := range tasks {
		sagaGroups[task.SagaId] = append(sagaGroups[task.SagaId], task)
	}
	return sagaGroups
}

// processSagaCompensations 处理指定 Saga 的补偿任务
func (s *CompensationProcessorService) processSagaCompensations(ctx context.Context, sagaID string, tasks []*entity.SagaSteps) (successCount, errorCount int) {
	g.Log().Infof(ctx, "开始处理 Saga %s 的补偿任务，共 %d 个", sagaID, len(tasks))

	// 检查 Saga 状态
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		g.Log().Errorf(ctx, "Saga 事务不存在: %s", sagaID)
		return 0, len(tasks)
	}

	if sagaTransaction == nil {
		g.Log().Errorf(ctx, "Saga 事务不存在: %s", sagaID)
		return 0, len(tasks)
	}

	// 确定处理原因
	var recoveryReason string
	switch sagaTransaction.SagaStatus {
	case consts.SagaStatusCompensating:
		recoveryReason = "Saga 正在补偿中，继续处理未完成的补偿任务"
	case consts.SagaStatusFailed:
		recoveryReason = "Saga 已失败，处理遗留的未完成补偿任务"
	default:
		recoveryReason = fmt.Sprintf("Saga 状态为 %s，处理未完成的补偿任务", sagaTransaction.SagaStatus)
	}

	g.Log().Infof(ctx, "Saga %s 状态: %s, 处理原因: %s",
		sagaID, sagaTransaction.SagaStatus, recoveryReason)

	// 按 step_index 逆序排序（补偿应该逆序执行）
	sortedTasks := s.sortTasksByStepIndexDesc(tasks)

	// 串行执行补偿任务
	for _, task := range sortedTasks {
		// 只处理未完成的任务
		if task.CompensationStatus == consts.CompensationStatusPending ||
			task.CompensationStatus == consts.CompensationStatusDelay ||
			task.CompensationStatus == consts.CompensationStatusRunning {
			success := s.executeCompensationTask(ctx, task)
			if success {
				successCount++
			} else {
				errorCount++
			}
		}
	}

	g.Log().Infof(ctx, "Saga %s 补偿任务处理完成: 成功=%d, 失败=%d", sagaID, successCount, errorCount)

	// 检查所有补偿动作是否已完成，如果是则更新状态为 SagaStatusFailed
	err = s.checkAndUpdateCompletionStatus(ctx, sagaID)
	if err != nil {
		g.Log().Errorf(ctx, "检查并更新Saga状态失败: SagaId=%s, Error=%v", sagaID, err)
	}

	return successCount, errorCount
}

// sortTasksByStepIndexDesc 按 step_index 逆序排序任务
func (s *CompensationProcessorService) sortTasksByStepIndexDesc(tasks []*entity.SagaSteps) []*entity.SagaSteps {
	// 创建副本避免修改原始数组
	sortedTasks := make([]*entity.SagaSteps, len(tasks))
	copy(sortedTasks, tasks)

	// 按 step_index 逆序排序
	for i := 0; i < len(sortedTasks)-1; i++ {
		for j := i + 1; j < len(sortedTasks); j++ {
			if sortedTasks[i].StepIndex < sortedTasks[j].StepIndex {
				sortedTasks[i], sortedTasks[j] = sortedTasks[j], sortedTasks[i]
			}
		}
	}

	return sortedTasks
}

// executeCompensationTask 执行单个补偿任务
func (s *CompensationProcessorService) executeCompensationTask(ctx context.Context, task *entity.SagaSteps) bool {
	g.Log().Infof(ctx, "处理补偿任务: SagaId=%s, StepId=%s, StepIndex=%d, Action=%s, Service=%s",
		task.SagaId, task.StepId, task.StepIndex, task.Action, task.ServiceName)

	// 调用现有的补偿执行方法
	input := &model.ExecuteCompensationInput{
		SagaId: task.SagaId,
		StepId: task.StepId,
	}

	output, err := s.sagaService.ExecuteCompensation(ctx, input)
	if err != nil {
		g.Log().Errorf(ctx, "执行补偿任务失败: SagaId=%s, StepId=%s, Error=%v",
			task.SagaId, task.StepId, err)
		return false
	}

	if !output.Success {
		g.Log().Errorf(ctx, "补偿任务执行失败: SagaId=%s, StepId=%s, Message=%s",
			task.SagaId, task.StepId, output.Message)
		return false
	}

	g.Log().Infof(ctx, "补偿任务执行成功: SagaId=%s, StepId=%s",
		task.SagaId, task.StepId)
	return true
}

// GetProcessingStatus 获取处理服务状态
func (s *CompensationProcessorService) GetProcessingStatus(ctx context.Context) (*model.RecoveryStatusOutput, error) {
	s.recoveryMutex.RLock()
	defer s.recoveryMutex.RUnlock()

	// 获取未完成补偿任务统计
	total, pending, running, delay, err := dao.SagaSteps.CountUnfinishedCompensations(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取未完成补偿任务统计失败: %w", err)
	}

	return &model.RecoveryStatusOutput{
		IsRecoveryRunning:    s.isRecoveryRunning,
		TaskTimeout:          s.taskTimeout,
		RecoveryInterval:     s.recoveryInterval,
		MaxConcurrentTasks:   s.maxConcurrentTasks,
		TotalUnfinishedTasks: total,
		PendingCompensations: pending,
		RunningCompensations: running,
		DelayCompensations:   delay,
		LastRecoveryTime:     gtime.Now(),
	}, nil
}

// checkAndUpdateCompletionStatus 检查补偿完成情况并更新Saga状态为failed
func (s *CompensationProcessorService) checkAndUpdateCompletionStatus(ctx context.Context, sagaID string) error {
	// 1. 查询当前 Saga 状态
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		return fmt.Errorf("查询Saga事务失败: %w", err)
	}

	if sagaTransaction == nil {
		return fmt.Errorf("Saga事务不存在: %s", sagaID)
	}

	// 2. 只有当前状态为 compensating 时才需要检查补偿完成情况
	if sagaTransaction.SagaStatus != consts.SagaStatusCompensating {
		g.Log().Infof(ctx, "Saga状态非compensating，跳过状态更新: SagaId=%s, 当前状态=%s",
			sagaID, sagaTransaction.SagaStatus)
		return nil
	}

	// 3. 获取补偿统计信息
	stats, err := dao.SagaSteps.GetCompensationStatistics(ctx, sagaID)
	if err != nil {
		return fmt.Errorf("获取补偿统计信息失败: %w", err)
	}

	g.Log().Infof(ctx, "补偿统计: SagaId=%s, 总步骤=%d, 已完成=%d, 失败=%d, 待处理=%d, 运行中=%d, 延期=%d",
		sagaID, stats.Total, stats.Completed, stats.Failed, stats.Pending, stats.Running, stats.Delay)

	// 4. 检查是否所有补偿都已完成（没有待处理、运行中、延期的补偿）
	unfinishedCount := stats.Pending + stats.Running + stats.Delay
	if unfinishedCount == 0 && stats.Total > 0 {
		// 5. 更新状态为 failed
		err = dao.SagaTransactions.UpdateStatus(ctx, sagaID, consts.SagaStatusFailed)
		if err != nil {
			return fmt.Errorf("更新Saga状态为failed失败: %w", err)
		}

		g.Log().Infof(ctx, "所有补偿动作已完成，Saga状态已更新为failed: SagaId=%s, 总步骤=%d, 已完成=%d, 失败=%d",
			sagaID, stats.Total, stats.Completed, stats.Failed)
	} else {
		g.Log().Infof(ctx, "补偿尚未全部完成，保持当前状态: SagaId=%s, 待处理=%d, 运行中=%d, 延期=%d",
			sagaID, stats.Pending, stats.Running, stats.Delay)
	}

	return nil
}
