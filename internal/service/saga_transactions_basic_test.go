package service

import (
	"context"
	"reflect"
	"saga/internal/consts"
	"saga/internal/model"
	"testing"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
)

func TestSagaTransactionsService_ReportCompensation(t *testing.T) {
	type args struct {
		ctx   context.Context
		input *model.ReportCompensationInput
	}
	tests := []struct {
		name    string
		s       *SagaTransactionsService
		args    args
		want    *model.ReportCompensationOutput
		wantErr bool
		setupDB func() // 设置测试数据的函数
	}{
		{
			name: "场景1：分布式事务不存在",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.ReportCompensationInput{
					SagaId:              "non-existent-saga-id",
					Action:              "test_action",
					ServiceName:         "test-service",
					ContextData:         `{"test": "data"}`,
					CompensationContext: `{"compensate": "data"}`,
					CompensateEndpoint:  "http://test-service/compensate",
				},
			},
			want:    nil,
			wantErr: true, // 期望返回错误，因为事务不存在
			setupDB: func() {
				// 不需要设置数据，因为我们要测试不存在的情况
			},
		},
		{
			name: "场景2：Auto模式正常运行",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.ReportCompensationInput{
					SagaId:              "test-saga-auto",
					Action:              "create_user",
					ServiceName:         "user-service",
					ContextData:         `{"userId": "12345"}`,
					CompensationContext: `{"action": "delete_user", "userId": "12345"}`,
					CompensateEndpoint:  "http://user-service/compensate/delete",
				},
			},
			want: &model.ReportCompensationOutput{
				Success: true,
			},
			wantErr: false,
			setupDB: func() {
				// 创建一个Auto模式的Saga事务
				createRealTestSagaTransaction("test-saga-auto", consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)
			},
		},
		{
			name: "场景3：Manual模式正常运行",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.ReportCompensationInput{
					SagaId:              "test-saga-manual",
					Action:              "lock_user",
					ServiceName:         "user-service",
					ContextData:         `{"userId": "12345", "lockType": "exclusive"}`,
					CompensationContext: `{"action": "unlock_user", "userId": "12345"}`,
					CompensateEndpoint:  "http://user-service/compensate/unlock",
				},
			},
			want: &model.ReportCompensationOutput{
				Success: true,
			},
			wantErr: false,
			setupDB: func() {
				// 创建一个Manual模式的Saga事务，包含步骤模板
				stepTemplates := `[
					{"step_index": 1, "service": "user-service", "action": "lock_user"},
					{"step_index": 2, "service": "stock-service", "action": "reserve_stock"},
					{"step_index": 3, "service": "order-service", "action": "create_order"}
				]`
				createRealTestSagaTransaction("test-saga-manual", consts.StepIndexModeManual, stepTemplates, 0, consts.SagaStatusPending)
			},
		},
		{
			name: "场景4：验证Pending状态更新为Running",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.ReportCompensationInput{
					SagaId:              "test-saga-pending",
					Action:              "process_payment",
					ServiceName:         "payment-service",
					ContextData:         `{"amount": 100, "currency": "USD"}`,
					CompensationContext: `{"action": "refund_payment", "amount": 100}`,
					CompensateEndpoint:  "http://payment-service/compensate/refund",
				},
			},
			want: &model.ReportCompensationOutput{
				Success: true,
			},
			wantErr: false,
			setupDB: func() {
				// 创建一个状态为Pending的Saga事务
				createRealTestSagaTransaction("test-saga-pending", consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)
			},
		},
		{
			name: "场景5：验证Running状态保持不变但StepIndex更新",
			s:    NewSagaTransactions(),
			args: args{
				ctx: context.Background(),
				input: &model.ReportCompensationInput{
					SagaId:              "test-saga-running",
					Action:              "send_notification",
					ServiceName:         "notification-service",
					ContextData:         `{"userId": "12345", "type": "email"}`,
					CompensationContext: `{"action": "cancel_notification", "userId": "12345"}`,
					CompensateEndpoint:  "http://notification-service/compensate/cancel",
				},
			},
			want: &model.ReportCompensationOutput{
				Success: true,
			},
			wantErr: false,
			setupDB: func() {
				// 创建一个状态为Running的Saga事务
				createRealTestSagaTransaction("test-saga-running", consts.StepIndexModeAuto, "", 1, consts.SagaStatusRunning)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 清理之前的测试数据
			cleanupTestData(t, tt.args.input.SagaId)

			// 设置测试数据
			if tt.setupDB != nil {
				tt.setupDB()
			}

			// 执行测试
			got, err := tt.s.ReportCompensation(tt.args.ctx, tt.args.input)

			// 验证错误
			if (err != nil) != tt.wantErr {
				t.Errorf("SagaTransactionsService.ReportCompensation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 验证返回值
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SagaTransactionsService.ReportCompensation() = %v, want %v", got, tt.want)
			}

			// 额外验证：检查数据库状态
			if !tt.wantErr && got != nil && got.Success {
				// 这里可以添加额外的验证逻辑
				// 1. 验证SagaSteps表中是否正确创建了记录
				// 2. 验证SagaTransactions表中的状态是否正确更新
				// 3. 验证Auto模式下的StepIndex是否正确递增
				t.Logf("测试成功：%s", tt.name)
			}

			// 清理测试后的数据
			cleanupTestData(t, tt.args.input.SagaId)
		})
	}
}

// 实际的测试示例：如何使用这些辅助函数
func TestSagaTransactionsService_ReportCompensation_Integration(t *testing.T) {
	// 这是一个集成测试示例，展示如何使用上述辅助函数

	// 测试场景1：不存在的事务
	t.Run("分布式事务不存在", func(t *testing.T) {
		service := NewSagaTransactions()
		input := &model.ReportCompensationInput{
			SagaId:              "non-existent-saga",
			Action:              "test_action",
			ServiceName:         "test-service",
			ContextData:         `{"test": "data"}`,
			CompensationContext: `{"compensate": "data"}`,
			CompensateEndpoint:  "http://test-service/compensate",
		}

		_, err := service.ReportCompensation(context.Background(), input)
		if err == nil {
			t.Error("期望返回错误，但没有错误")
		}
		t.Logf("正确返回错误: %v", err)
	})

	// 测试场景2：Auto模式正常运行
	t.Run("Auto模式正常运行", func(t *testing.T) {
		sagaID := "test-saga-auto-integration"

		// 准备测试数据
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)
		defer cleanupTestData(t, sagaID)

		// 执行测试
		service := NewSagaTransactions()
		input := &model.ReportCompensationInput{
			SagaId:              sagaID,
			Action:              "create_user",
			ServiceName:         "user-service",
			ContextData:         `{"userId": "12345"}`,
			CompensationContext: `{"action": "delete_user", "userId": "12345"}`,
			CompensateEndpoint:  "http://user-service/compensate/delete",
		}

		result, err := service.ReportCompensation(context.Background(), input)
		if err != nil {
			t.Fatalf("ReportCompensation失败: %v", err)
		}

		if !result.Success {
			t.Error("期望成功，但返回失败")
		}

		// 验证结果：检查事务状态是否更新为Running
		verifySagaTransactionStatus(t, sagaID, consts.SagaStatusRunning)

		// 验证步骤是否正确创建
		verifySagaStepCreated(t, sagaID, "create_user", "user-service", 1)

		// 验证步骤索引是否正确递增
		verifySagaStepIndexIncremented(t, sagaID, 1)

		t.Logf("Auto模式测试完成: SagaId=%s", sagaID)
	})

	// 测试场景3：Manual模式正常运行
	t.Run("Manual模式正常运行", func(t *testing.T) {
		sagaID := "test-saga-manual-integration"
		stepTemplates := generateStepTemplates()

		// 准备测试数据
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeManual, stepTemplates, 0, consts.SagaStatusPending)
		defer cleanupTestData(t, sagaID)

		// 执行测试
		service := NewSagaTransactions()
		input := &model.ReportCompensationInput{
			SagaId:              sagaID,
			Action:              "lock_user",
			ServiceName:         "user-service",
			ContextData:         `{"userId": "12345"}`,
			CompensationContext: `{"action": "unlock_user", "userId": "12345"}`,
			CompensateEndpoint:  "http://user-service/compensate/unlock",
		}

		result, err := service.ReportCompensation(context.Background(), input)
		if err != nil {
			t.Fatalf("ReportCompensation失败: %v", err)
		}

		if !result.Success {
			t.Error("期望成功，但返回失败")
		}

		// 验证结果：检查事务状态是否更新为Running
		verifySagaTransactionStatus(t, sagaID, consts.SagaStatusRunning)

		// 验证步骤是否正确创建（根据模板，应该是步骤1）
		verifySagaStepCreated(t, sagaID, "lock_user", "user-service", 1)

		t.Logf("Manual模式测试完成: SagaId=%s", sagaID)
	})

	// 测试场景4：多步骤Auto模式集成测试
	t.Run("多步骤Auto模式集成测试", func(t *testing.T) {
		sagaID := "test-saga-multi-step-auto"

		// 准备测试数据
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)
		defer cleanupTestData(t, sagaID)

		service := NewSagaTransactions()

		// 第一步：用户服务
		input1 := &model.ReportCompensationInput{
			SagaId:              sagaID,
			Action:              "create_user",
			ServiceName:         "user-service",
			ContextData:         `{"userId": "12345", "userName": "testuser"}`,
			CompensationContext: `{"action": "delete_user", "userId": "12345"}`,
			CompensateEndpoint:  "http://user-service/compensate/delete",
		}

		result1, err := service.ReportCompensation(context.Background(), input1)
		if err != nil {
			t.Fatalf("第一步ReportCompensation失败: %v", err)
		}
		if !result1.Success {
			t.Error("第一步期望成功，但返回失败")
		}

		// 验证第一步结果
		verifySagaTransactionStatus(t, sagaID, consts.SagaStatusRunning)
		verifySagaStepCreated(t, sagaID, "create_user", "user-service", 1)
		verifySagaStepIndexIncremented(t, sagaID, 1)

		// 第二步：支付服务
		input2 := &model.ReportCompensationInput{
			SagaId:              sagaID,
			Action:              "process_payment",
			ServiceName:         "payment-service",
			ContextData:         `{"amount": 100.00, "currency": "USD"}`,
			CompensationContext: `{"action": "refund_payment", "amount": 100.00}`,
			CompensateEndpoint:  "http://payment-service/compensate/refund",
		}

		result2, err := service.ReportCompensation(context.Background(), input2)
		if err != nil {
			t.Fatalf("第二步ReportCompensation失败: %v", err)
		}
		if !result2.Success {
			t.Error("第二步期望成功，但返回失败")
		}

		// 验证第二步结果
		verifySagaStepCreated(t, sagaID, "process_payment", "payment-service", 2)
		verifySagaStepIndexIncremented(t, sagaID, 2)

		// 验证总步骤数
		verifySagaStepsCompleted(t, sagaID, 2)

		t.Logf("多步骤Auto模式集成测试完成: SagaId=%s", sagaID)
	})
}
