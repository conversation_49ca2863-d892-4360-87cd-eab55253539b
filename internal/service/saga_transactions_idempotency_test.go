package service

import (
	"context"
	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"testing"

	"github.com/gogf/gf/v2/frame/g"
)

// TestReportCompensation_IdempotencyOptimization 测试ReportCompensation的幂等性优化
func TestReportCompensation_IdempotencyOptimization(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactions()

	// 创建测试事务
	createOutput, err := service.CreateSagaTransaction(ctx, &model.CreateSagaTransactionInput{
		Name:          "幂等性测试事务",
		StepIndexMode: consts.StepIndexModeAuto,
	})
	if err != nil {
		t.Fatalf("创建事务失败: %v", err)
	}

	sagaID := createOutput.SagaId

	// 清理测试数据
	defer cleanupTestData(t, sagaID)

	// 第一次上报补偿信息
	input := &model.ReportCompensationInput{
		SagaId:              sagaID,
		Action:              "test_action",
		ServiceName:         "test-service",
		ContextData:         `{"test": "data"}`,
		CompensationContext: `{"compensate": "data"}`,
		CompensateEndpoint:  "http://test-service/compensate",
	}

	result1, err := service.ReportCompensation(ctx, input)
	if err != nil {
		t.Fatalf("第一次上报失败: %v", err)
	}

	if !result1.Success {
		t.Error("第一次上报应该成功")
	}

	// 第二次上报相同的补偿信息（幂等性测试）
	result2, err := service.ReportCompensation(ctx, input)
	if err != nil {
		t.Fatalf("第二次上报失败: %v", err)
	}

	if !result2.Success {
		t.Error("第二次上报应该成功（幂等性）")
	}

	// 验证数据库中只有一条记录
	steps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询步骤失败: %v", err)
	}

	if len(steps) != 1 {
		t.Errorf("期望只有1个步骤记录，实际有%d个", len(steps))
	}

	// 验证事务状态
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询事务失败: %v", err)
	}

	if sagaTransaction.SagaStatus != consts.SagaStatusRunning {
		t.Errorf("事务状态应该是%s，实际是%s", consts.SagaStatusRunning, sagaTransaction.SagaStatus)
	}

	if sagaTransaction.CurStepIndex != 1 {
		t.Errorf("当前步骤索引应该是1，实际是%d", sagaTransaction.CurStepIndex)
	}

	t.Log("✅ ReportCompensation幂等性优化测试通过")
}

// TestReportCompensation_DelayedMessageHandling 测试ReportCompensation的延迟消息处理
func TestReportCompensation_DelayedMessageHandling(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactions()

	// 创建测试事务
	createOutput, err := service.CreateSagaTransaction(ctx, &model.CreateSagaTransactionInput{
		Name:          "延迟消息测试事务",
		StepIndexMode: consts.StepIndexModeAuto,
	})
	if err != nil {
		t.Fatalf("创建事务失败: %v", err)
	}

	sagaID := createOutput.SagaId

	// 清理测试数据
	defer cleanupTestData(t, sagaID)

	// 先上报步骤2（模拟延迟消息）
	input2 := &model.ReportCompensationInput{
		SagaId:              sagaID,
		Action:              "step2_action",
		ServiceName:         "service2",
		ContextData:         `{"step": 2}`,
		CompensationContext: `{"compensate": "step2"}`,
		CompensateEndpoint:  "http://service2/compensate",
	}

	result2, err := service.ReportCompensation(ctx, input2)
	if err != nil {
		t.Fatalf("上报步骤2失败: %v", err)
	}

	if !result2.Success {
		t.Error("上报步骤2应该成功")
	}

	// 再上报步骤1（模拟延迟到达的消息）
	input1 := &model.ReportCompensationInput{
		SagaId:              sagaID,
		Action:              "step1_action",
		ServiceName:         "service1",
		ContextData:         `{"step": 1}`,
		CompensationContext: `{"compensate": "step1"}`,
		CompensateEndpoint:  "http://service1/compensate",
	}

	result1, err := service.ReportCompensation(ctx, input1)
	if err != nil {
		t.Fatalf("上报步骤1失败: %v", err)
	}

	if !result1.Success {
		t.Error("上报步骤1应该成功")
	}

	// 验证数据库中有两条记录
	steps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询步骤失败: %v", err)
	}

	if len(steps) != 2 {
		t.Errorf("期望有2个步骤记录，实际有%d个", len(steps))
	}

	// 验证步骤索引正确
	stepIndexMap := make(map[int]string)
	for _, step := range steps {
		stepIndexMap[step.StepIndex] = step.Action
	}

	if stepIndexMap[1] != "step1_action" {
		t.Errorf("步骤1的动作应该是step1_action，实际是%s", stepIndexMap[1])
	}

	if stepIndexMap[2] != "step2_action" {
		t.Errorf("步骤2的动作应该是step2_action，实际是%s", stepIndexMap[2])
	}

	// 验证事务状态
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询事务失败: %v", err)
	}

	if sagaTransaction.CurStepIndex != 2 {
		t.Errorf("当前步骤索引应该是2，实际是%d", sagaTransaction.CurStepIndex)
	}

	t.Log("✅ ReportCompensation延迟消息处理测试通过")
}

// TestReportCompensation_CompensatingStateHandling 测试ReportCompensation在补偿状态下的处理
func TestReportCompensation_CompensatingStateHandling(t *testing.T) {
	// 创建测试上下文
	ctx := context.Background()
	service := NewSagaTransactions()

	// 创建测试事务
	createOutput, err := service.CreateSagaTransaction(ctx, &model.CreateSagaTransactionInput{
		Name:          "补偿状态测试事务",
		StepIndexMode: consts.StepIndexModeAuto,
	})
	if err != nil {
		t.Fatalf("创建事务失败: %v", err)
	}

	sagaID := createOutput.SagaId

	// 清理测试数据
	defer cleanupTestData(t, sagaID)

	// 步骤1：上报一个正常的补偿信息
	_, err = service.ReportCompensation(ctx, &model.ReportCompensationInput{
		SagaId:              sagaID,
		Action:              "existing_action",
		ServiceName:         "existing-service",
		ContextData:         `{"test": "data"}`,
		CompensationContext: `{"compensate": "data"}`,
		CompensateEndpoint:  "http://test-service/compensate",
	})
	if err != nil {
		t.Fatalf("上报补偿信息失败: %v", err)
	}

	// 步骤2：手动将事务状态设置为compensating
	_, err = dao.SagaTransactions.Ctx(ctx).Where("saga_id", sagaID).Update(g.Map{
		"saga_status": consts.SagaStatusCompensating,
	})
	if err != nil {
		t.Fatalf("更新事务状态失败: %v", err)
	}

	// 步骤3：尝试在compensating状态下上报新的补偿信息
	result, err := service.ReportCompensation(ctx, &model.ReportCompensationInput{
		SagaId:              sagaID,
		Action:              "new_action",
		ServiceName:         "new-service",
		ContextData:         `{"new": "data"}`,
		CompensationContext: `{"new_compensate": "data"}`,
		CompensateEndpoint:  "http://new-service/compensate",
	})

	// 验证结果：应该拒绝新的补偿信息上报
	if err == nil {
		t.Error("在compensating状态下上报新补偿信息应该失败")
	}

	if result != nil && result.Success {
		t.Error("在compensating状态下上报新补偿信息不应该成功")
	}

	// 验证数据库中只有原来的步骤记录
	steps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询步骤失败: %v", err)
	}

	if len(steps) != 1 {
		t.Errorf("期望只有1个步骤记录，实际有%d个", len(steps))
	}

	if len(steps) > 0 && steps[0].Action != "existing_action" {
		t.Errorf("步骤动作应该是existing_action，实际是%s", steps[0].Action)
	}

	t.Log("✅ ReportCompensation补偿状态处理测试通过")
}

// TestReportCompensation_ConcurrentDuplicateStepPrevention 测试ReportCompensation的并发重复步骤防护
func TestReportCompensation_ConcurrentDuplicateStepPrevention(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactions()

	// 创建测试事务
	createOutput, err := service.CreateSagaTransaction(ctx, &model.CreateSagaTransactionInput{
		Name:          "并发重复步骤防护测试",
		StepIndexMode: consts.StepIndexModeAuto,
	})
	if err != nil {
		t.Fatalf("创建事务失败: %v", err)
	}

	sagaID := createOutput.SagaId

	// 清理测试数据
	defer cleanupTestData(t, sagaID)

	// 创建相同的补偿信息输入
	input := &model.ReportCompensationInput{
		SagaId:              sagaID,
		Action:              "concurrent_action",
		ServiceName:         "concurrent-service",
		ContextData:         `{"concurrent": "data"}`,
		CompensationContext: `{"concurrent_compensate": "data"}`,
		CompensateEndpoint:  "http://concurrent-service/compensate",
	}

	// 模拟并发调用（这里串行调用来模拟）
	results := make([]*model.ReportCompensationOutput, 3)
	errors := make([]error, 3)

	for i := 0; i < 3; i++ {
		results[i], errors[i] = service.ReportCompensation(ctx, input)
	}

	// 验证所有调用都成功（幂等性）
	successCount := 0
	for i := 0; i < 3; i++ {
		if errors[i] == nil && results[i] != nil && results[i].Success {
			successCount++
		}
	}

	if successCount != 3 {
		t.Errorf("期望3次调用都成功，实际成功%d次", successCount)
	}

	// 验证数据库中只有一条记录
	steps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询步骤失败: %v", err)
	}

	if len(steps) != 1 {
		t.Errorf("期望只有1个步骤记录，实际有%d个", len(steps))
	}

	// 验证事务状态
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	if err != nil {
		t.Fatalf("查询事务失败: %v", err)
	}

	if sagaTransaction.CurStepIndex != 1 {
		t.Errorf("当前步骤索引应该是1，实际是%d", sagaTransaction.CurStepIndex)
	}

	t.Log("✅ ReportCompensation并发重复步骤防护测试通过")
}

// TestIdempotencyCommitAndRollback 测试提交和回滚的幂等性
func TestIdempotencyCommitAndRollback(t *testing.T) {
	service := NewSagaTransactions()
	ctx := context.Background()

	// 测试 CommitSagaTransaction 的幂等性
	t.Run("CommitSagaTransaction 幂等性", func(t *testing.T) {
		sagaID := "test-commit-idempotency"
		defer cleanupTestData(t, sagaID)

		// 1. 创建一个已完成的事务
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 3, consts.SagaStatusCompleted)
		createTestSagaStep(t, sagaID, "step1", "service1", 1)
		createTestSagaStep(t, sagaID, "step2", "service2", 2)
		createTestSagaStep(t, sagaID, "step3", "service3", 3)

		// 2. 第一次调用 CommitSagaTransaction（幂等性）
		result1, err := service.CommitSagaTransaction(ctx, &model.CommitSagaTransactionInput{
			SagaId: sagaID,
		})
		if err != nil {
			t.Fatalf("第一次调用失败: %v", err)
		}
		if !result1.Success {
			t.Errorf("第一次调用应该成功（幂等性）")
		}
		if result1.Message != "事务已完成（幂等性）" {
			t.Errorf("第一次调用消息不正确: got %s", result1.Message)
		}

		// 3. 第二次调用 CommitSagaTransaction（幂等性）
		result2, err := service.CommitSagaTransaction(ctx, &model.CommitSagaTransactionInput{
			SagaId: sagaID,
		})
		if err != nil {
			t.Fatalf("第二次调用失败: %v", err)
		}
		if !result2.Success {
			t.Errorf("第二次调用应该成功（幂等性）")
		}
		if result2.Message != "事务已完成（幂等性）" {
			t.Errorf("第二次调用消息不正确: got %s", result2.Message)
		}

		// 4. 验证两次调用的结果应该一致
		if result1.SagaId != result2.SagaId {
			t.Errorf("两次调用的SagaId不一致: %s vs %s", result1.SagaId, result2.SagaId)
		}
		if result1.NewStatus != result2.NewStatus {
			t.Errorf("两次调用的状态不一致: %s vs %s", result1.NewStatus, result2.NewStatus)
		}
		if result1.CompletedSteps != result2.CompletedSteps {
			t.Errorf("两次调用的完成步骤数不一致: %d vs %d", result1.CompletedSteps, result2.CompletedSteps)
		}
		if result1.ExpectedSteps != result2.ExpectedSteps {
			t.Errorf("两次调用的预期步骤数不一致: %d vs %d", result1.ExpectedSteps, result2.ExpectedSteps)
		}

		t.Log("✅ CommitSagaTransaction 幂等性测试通过")
	})

	t.Log("🎉 所有幂等性测试通过！")
}
