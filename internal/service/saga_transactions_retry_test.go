package service

import (
	"context"
	"errors"
	"fmt"
	"saga/internal/consts"
	"saga/internal/model/entity"
	"strings"
	"testing"
	"time"
)

// TestSagaTransactionsService_compensationCallRetry 测试补偿调用重试机制
func TestSagaTransactionsService_compensationCallRetry(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactionsWithRetryConfig(2, 100*time.Millisecond, 1*time.Second, 2.0)

	step := &entity.SagaSteps{
		StepId:              "test-step-retry",
		SagaId:              "test-saga-retry",
		Action:              "TestAction",
		StepIndex:           1,
		ServiceName:         "test-service",
		ContextData:         "{}",
		CompensationContext: "{}",
		CompensateEndpoint:  "http://httpbin.org/status/500", // 模拟服务器错误
		CompensationStatus:  "pending",
		LastError:           "",
		RetryCount:          0,
	}

	success, errorMsg, retryCount := service.compensationCall(ctx, step, 5000)
	if success {
		t.Error("Expected compensationCall to return false for persistent 500 errors")
	}

	if !strings.Contains(errorMsg, "HTTP错误") {
		t.<PERSON><PERSON><PERSON>("Expected error message to contain 'HTTP错误', got: %s", errorMsg)
	}

	if retryCount < 2 {
		t.Errorf("Expected at least 2 retry attempts, got: %d", retryCount)
	}
}

// TestSagaTransactionsService_isNetworkError 测试网络错误判断
func TestSagaTransactionsService_isNetworkError(t *testing.T) {
	service := NewSagaTransactions()

	testCases := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "超时错误",
			err:      errors.New("timeout"),
			expected: true,
		},
		{
			name:     "连接错误",
			err:      errors.New("connection refused"),
			expected: true,
		},
		{
			name:     "DNS错误",
			err:      errors.New("no such host"),
			expected: true,
		},
		{
			name:     "网络不可达",
			err:      errors.New("network unreachable"),
			expected: true,
		},
		{
			name:     "普通错误",
			err:      errors.New("some other error"),
			expected: false,
		},
		{
			name:     "空错误",
			err:      nil,
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := service.isNetworkError(tc.err)
			if result != tc.expected {
				t.Errorf("期望 %v, 实际 %v", tc.expected, result)
			}
		})
	}
}

// TestSagaTransactionsService_calculateRetryDelay 测试重试延迟计算
func TestSagaTransactionsService_calculateRetryDelay(t *testing.T) {
	service := NewSagaTransactionsWithRetryConfig(
		5,                    // 最大重试次数
		100*time.Millisecond, // 初始间隔
		5*time.Second,        // 最大间隔
		2.0,                  // 退避因子
	)

	testCases := []struct {
		retryCount    int
		expectedDelay time.Duration
	}{
		{0, 100 * time.Millisecond},  // 第一次重试
		{1, 200 * time.Millisecond},  // 第二次重试
		{2, 400 * time.Millisecond},  // 第三次重试
		{3, 800 * time.Millisecond},  // 第四次重试
		{4, 1600 * time.Millisecond}, // 第五次重试
		{5, 3200 * time.Millisecond}, // 第六次重试
		{10, 5 * time.Second},        // 超过最大间隔，使用最大间隔
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("重试次数_%d", tc.retryCount), func(t *testing.T) {
			delay := service.calculateRetryDelay(tc.retryCount)

			// 对于超过最大间隔的情况，应该使用最大间隔
			if tc.retryCount >= 6 {
				if delay != 5*time.Second {
					t.Errorf("重试次数%d，期望延迟%v，实际延迟%v", tc.retryCount, 5*time.Second, delay)
				}
			} else {
				if delay != tc.expectedDelay {
					t.Errorf("重试次数%d，期望延迟%v，实际延迟%v", tc.retryCount, tc.expectedDelay, delay)
				}
			}
		})
	}
}

// TestSagaTransactionsService_compensationCallSuccess 测试补偿调用成功（不需要重试）
func TestSagaTransactionsService_compensationCallSuccess(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactions()

	step := &entity.SagaSteps{
		StepId:              "test-step-success",
		SagaId:              "test-saga-success",
		Action:              "TestAction",
		StepIndex:           1,
		ServiceName:         "test-service",
		ContextData:         "{}",
		CompensationContext: "{}",
		CompensateEndpoint:  "http://httpbin.org/status/200", // 成功端点
		CompensationStatus:  "pending",
		LastError:           "",
		RetryCount:          0,
	}

	success, errorMsg, retryCount := service.compensationCall(ctx, step, 5000)

	if !success {
		t.Error("Expected compensationCall to return true for successful request")
	}

	if errorMsg != "" {
		t.Errorf("Expected empty error message for success, got: %s", errorMsg)
	}

	if retryCount != 0 {
		t.Errorf("Expected 0 retries for successful request, got: %d", retryCount)
	}
}

// TestSagaTransactionsService_compensationCallClientError 测试补偿调用客户端错误（不重试）
func TestSagaTransactionsService_compensationCallClientError(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactions()

	step := &entity.SagaSteps{
		StepId:              "test-step-client-error",
		SagaId:              "test-saga-client-error",
		Action:              "TestAction",
		StepIndex:           1,
		ServiceName:         "test-service",
		ContextData:         "{}",
		CompensationContext: "{}",
		CompensateEndpoint:  "http://httpbin.org/status/400", // 客户端错误端点
		CompensationStatus:  "pending",
		LastError:           "",
		RetryCount:          0,
	}

	success, errorMsg, retryCount := service.compensationCall(ctx, step, 5000)

	if success {
		t.Error("Expected compensationCall to return false for client error (400)")
	}

	if !strings.Contains(errorMsg, "HTTP错误") || !strings.Contains(errorMsg, "400") {
		t.Errorf("Expected error message to contain 'HTTP错误' and '400', got: %s", errorMsg)
	}

	if retryCount != 0 {
		t.Errorf("Expected 0 retries for client error, got: %d", retryCount)
	}
}

// TestSagaTransactionsService_NewSagaTransactionsWithRetryConfig 测试自定义重试配置构造函数
func TestSagaTransactionsService_NewSagaTransactionsWithRetryConfig(t *testing.T) {
	maxRetries := 5
	initialInterval := 500 * time.Millisecond
	maxInterval := 1 * time.Minute
	multiplier := 3.0

	service := NewSagaTransactionsWithRetryConfig(maxRetries, initialInterval, maxInterval, multiplier)

	if service.maxRetries != maxRetries {
		t.Errorf("Expected maxRetries %d, got %d", maxRetries, service.maxRetries)
	}
	if service.initialRetryInterval != initialInterval {
		t.Errorf("Expected initialRetryInterval %v, got %v", initialInterval, service.initialRetryInterval)
	}
	if service.maxRetryInterval != maxInterval {
		t.Errorf("Expected maxRetryInterval %v, got %v", maxInterval, service.maxRetryInterval)
	}
	if service.retryMultiplier != multiplier {
		t.Errorf("Expected retryMultiplier %f, got %f", multiplier, service.retryMultiplier)
	}
}

// TestSagaTransactionsService_DefaultRetryConfig 测试默认重试配置
func TestSagaTransactionsService_DefaultRetryConfig(t *testing.T) {
	service := NewSagaTransactions()

	if service.maxRetries != consts.DefaultMaxRetries {
		t.Errorf("Expected maxRetries %d, got %d", consts.DefaultMaxRetries, service.maxRetries)
	}
	if service.initialRetryInterval != consts.DefaultInitialRetryInterval {
		t.Errorf("Expected initialRetryInterval %v, got %v", consts.DefaultInitialRetryInterval, service.initialRetryInterval)
	}
	if service.maxRetryInterval != consts.DefaultMaxRetryInterval {
		t.Errorf("Expected maxRetryInterval %v, got %v", consts.DefaultMaxRetryInterval, service.maxRetryInterval)
	}
	if service.retryMultiplier != consts.DefaultRetryMultiplier {
		t.Errorf("Expected retryMultiplier %f, got %f", consts.DefaultRetryMultiplier, service.retryMultiplier)
	}
	// 验证新增的 ReportCompensation 重试配置
	if service.reportMaxRetries != consts.DefaultReportMaxRetries {
		t.Errorf("Expected reportMaxRetries %d, got %d", consts.DefaultReportMaxRetries, service.reportMaxRetries)
	}
	if service.reportRetryDelay != consts.DefaultReportRetryDelay {
		t.Errorf("Expected reportRetryDelay %v, got %v", consts.DefaultReportRetryDelay, service.reportRetryDelay)
	}
}

// TestSagaTransactionsService_NewSagaTransactionsWithFullConfig 测试完整配置构造函数
func TestSagaTransactionsService_NewSagaTransactionsWithFullConfig(t *testing.T) {
	// 补偿重试配置
	maxRetries := 5
	initialInterval := 500 * time.Millisecond
	maxInterval := 1 * time.Minute
	multiplier := 3.0
	timeout := 45 * time.Second
	compensationWindow := 30

	// ReportCompensation 重试配置
	reportMaxRetries := 2
	reportRetryDelay := 100 * time.Millisecond

	service := NewSagaTransactionsWithFullConfig(
		maxRetries, initialInterval, maxInterval, multiplier, timeout,
		reportMaxRetries, reportRetryDelay,
		compensationWindow,
	)

	// 验证补偿重试配置
	if service.maxRetries != maxRetries {
		t.Errorf("Expected maxRetries %d, got %d", maxRetries, service.maxRetries)
	}
	if service.initialRetryInterval != initialInterval {
		t.Errorf("Expected initialRetryInterval %v, got %v", initialInterval, service.initialRetryInterval)
	}
	if service.maxRetryInterval != maxInterval {
		t.Errorf("Expected maxRetryInterval %v, got %v", maxInterval, service.maxRetryInterval)
	}
	if service.retryMultiplier != multiplier {
		t.Errorf("Expected retryMultiplier %f, got %f", multiplier, service.retryMultiplier)
	}
	if service.compensationTimeout != timeout {
		t.Errorf("Expected compensationTimeout %v, got %v", timeout, service.compensationTimeout)
	}

	// 验证 ReportCompensation 重试配置
	if service.reportMaxRetries != reportMaxRetries {
		t.Errorf("Expected reportMaxRetries %d, got %d", reportMaxRetries, service.reportMaxRetries)
	}
	if service.reportRetryDelay != reportRetryDelay {
		t.Errorf("Expected reportRetryDelay %v, got %v", reportRetryDelay, service.reportRetryDelay)
	}
}

// TestIsRetryableHTTPError 测试HTTP状态码重试逻辑
func TestIsRetryableHTTPError(t *testing.T) {
	service := NewSagaTransactions()

	testCases := []struct {
		name            string
		statusCode      int
		expectedRetry   bool
		expectedMessage string
	}{
		// 成功状态码
		{"成功200", 200, false, "成功"},
		{"成功201", 201, false, "成功"},
		{"成功204", 204, false, "成功"},

		// 4xx 客户端错误 - 大部分不可重试
		{"客户端错误400", 400, false, "客户端错误"},
		{"客户端错误401", 401, false, "客户端错误"},
		{"客户端错误403", 403, false, "客户端错误"},
		{"客户端错误404", 404, false, "客户端错误"},

		// 4xx 特殊情况 - 可重试
		{"请求超时408", 408, true, "请求超时"},
		{"请求过多429", 429, true, "请求过多"},

		// 5xx 服务器错误 - 大部分可重试
		{"服务器错误500", 500, true, "服务器错误"},
		{"服务器错误502", 502, true, "服务器错误"},
		{"服务器错误503", 503, true, "服务器错误"},
		{"服务器错误504", 504, true, "服务器错误"},

		// 5xx 特殊情况 - 不可重试
		{"未实现501", 501, false, "未实现"},
		{"HTTP版本不支持505", 505, false, "HTTP版本不支持"},

		// 其他状态码
		{"未知状态码999", 999, false, "未知错误"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			isRetryable, errorType := service.isRetryableHTTPError(tc.statusCode)

			if isRetryable != tc.expectedRetry {
				t.Errorf("状态码 %d: 期望重试=%v, 实际重试=%v", tc.statusCode, tc.expectedRetry, isRetryable)
			}

			if errorType != tc.expectedMessage {
				t.Errorf("状态码 %d: 期望消息='%s', 实际消息='%s'", tc.statusCode, tc.expectedMessage, errorType)
			}
		})
	}
}

// TestCompensationCallWithHttpErrors 测试补偿调用的HTTP错误重试逻辑
func TestCompensationCallWithHTTPErrors(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name           string
		statusCode     int
		expectedRetry  bool
		maxRetries     int
		expectedResult bool
	}{
		{"成功200", 200, false, 3, true},
		{"客户端错误400", 400, false, 3, false},
		{"请求超时408", 408, true, 3, false},  // 会重试但最终失败
		{"服务器错误500", 500, true, 3, false}, // 会重试但最终失败
		{"请求过多429", 429, true, 3, false},  // 会重试但最终失败
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 跳过网络测试，因为需要真实的网络环境
			if tc.statusCode != 200 {
				t.Skip("跳过网络错误测试，需要真实网络环境")
			}

			service := NewSagaTransactionsWithRetryConfig(tc.maxRetries, 100*time.Millisecond, 1*time.Second, 2.0)

			step := createTestSagaStepForHTTPTest()
			step.CompensateEndpoint = "https://httpbin.org/status/" + fmt.Sprintf("%d", tc.statusCode)

			success, _, _ := service.compensationCall(ctx, step, 5000)

			if success != tc.expectedResult {
				t.Errorf("状态码 %d: 期望结果=%v, 实际结果=%v", tc.statusCode, tc.expectedResult, success)
			}
		})
	}
}
