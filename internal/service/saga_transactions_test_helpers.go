package service

import (
	"context"
	"fmt"
	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model/entity"
	"testing"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/guid"
)

// createRealTestSagaTransaction 真正创建测试用的Saga事务
func createRealTestSagaTransaction(sagaID string, stepIndexMode string, stepTemplates string, currentStepIndex int, status string) {
	// 创建测试用的Saga事务记录
	sagaTransaction := &entity.SagaTransactions{
		SagaId:        sagaID,
		Name:          "测试事务_" + sagaID,
		SagaStatus:    status,
		StepIndexMode: stepIndexMode,
		StepTemplates: stepTemplates,
		CurStepIndex:  currentStepIndex,
		CreatedAt:     gtime.Now(),
		UpdatedAt:     gtime.Now(),
	}

	// 真正插入数据库
	_, err := dao.SagaTransactions.Ctx(context.Background()).Insert(sagaTransaction)
	if err != nil {
		// 打印错误，但不要直接返回，让调用者知道出了问题
		fmt.Printf("创建测试Saga事务失败: SagaId=%s, Error=%v\n", sagaID, err)
		return
	}
	fmt.Printf("成功创建测试Saga事务: SagaId=%s, Mode=%s, Status=%s\n", sagaID, stepIndexMode, status)
}

// generateStepTemplates 测试辅助函数：生成Manual模式的步骤模板
func generateStepTemplates() string {
	return `[
		{"step_index": 1, "service": "user-service", "action": "lock_user"},
		{"step_index": 2, "service": "payment-service", "action": "process_payment"},
		{"step_index": 3, "service": "notification-service", "action": "send_notification"}
	]`
}

// createTestSagaStep 创建测试步骤数据的辅助函数
func createTestSagaStep(t *testing.T, sagaID string, action string, serviceName string, stepIndex int) {
	stepData := &entity.SagaSteps{
		StepId:              guid.S(), // 添加唯一的步骤ID
		SagaId:              sagaID,
		Action:              action,
		ServiceName:         serviceName,
		StepIndex:           stepIndex,
		ContextData:         "{}",                             // 空 JSON 对象
		CompensationContext: "{}",                             // 空 JSON 对象
		CompensateEndpoint:  "http://example.com/compensate",  // 示例补偿端点
		CompensationStatus:  consts.CompensationStatusPending, // 使用有效的补偿状态
		LastError:           "",                               // 空字符串
		RetryCount:          0,                                // 默认重试次数
		CreatedAt:           gtime.Now(),
		UpdatedAt:           gtime.Now(),
	}

	_, err := dao.SagaSteps.Ctx(context.Background()).Insert(stepData)
	if err != nil {
		t.Fatalf("创建测试步骤数据失败: %v", err)
	}
}

// createTestSagaStepWithCompensationStatus 创建带有指定补偿状态的测试步骤
func createTestSagaStepWithCompensationStatus(t *testing.T, sagaID, action, serviceName string, stepIndex int, compensationStatus string) {
	step := &entity.SagaSteps{
		StepId:              guid.S(),
		SagaId:              sagaID,
		Action:              action,
		ServiceName:         serviceName,
		StepIndex:           stepIndex,
		ContextData:         "{}",
		CompensationContext: "{}",
		CompensateEndpoint:  "http://example.com/compensate",
		CompensationStatus:  compensationStatus,
		LastError:           "",
		RetryCount:          0,
		CreatedAt:           gtime.Now(),
		UpdatedAt:           gtime.Now(),
	}

	_, err := dao.SagaSteps.Ctx(context.Background()).Insert(step)
	if err != nil {
		t.Fatalf("创建测试步骤失败: %v", err)
	}
}

// createTestSagaStepForHTTPTest 创建用于HTTP测试的步骤
func createTestSagaStepForHTTPTest() *entity.SagaSteps {
	return &entity.SagaSteps{
		StepId:              guid.S(),
		SagaId:              "test-saga-http",
		Action:              "test_action",
		ServiceName:         "test-service",
		StepIndex:           1,
		ContextData:         "{}",
		CompensationContext: "{}",
		CompensateEndpoint:  "http://test-service/compensate",
		CompensationStatus:  consts.CompensationStatusPending,
		LastError:           "",
		RetryCount:          0,
		CreatedAt:           gtime.Now(),
		UpdatedAt:           gtime.Now(),
	}
}

// cleanupTestData 清理测试数据 - 使用物理删除
func cleanupTestData(t *testing.T, sagaID string) {
	ctx := context.Background()

	// 物理删除步骤记录
	_, err := g.DB().Exec(ctx, "DELETE FROM saga_steps WHERE saga_id = ?", sagaID)
	if err != nil {
		t.Logf("物理删除步骤记录失败: %v", err)
	}

	// 物理删除 Saga 事务记录
	_, err = g.DB().Exec(ctx, "DELETE FROM saga_transactions WHERE saga_id = ?", sagaID)
	if err != nil {
		t.Logf("物理删除 Saga 事务记录失败: %v", err)
	}
}

// verifySagaTransactionCommitted 测试辅助函数：验证提交后的数据库状态
func verifySagaTransactionCommitted(t *testing.T, sagaID string) {
	t.Logf("验证事务提交后的状态: SagaId=%s", sagaID)

	// 查询事务状态
	result, err := dao.SagaTransactions.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询提交后的事务状态失败: %v", err)
	}

	if result.SagaStatus != consts.SagaStatusCompleted {
		t.Errorf("事务状态不正确: 期望=%s, 实际=%s", consts.SagaStatusCompleted, result.SagaStatus)
	}

	t.Log("事务状态验证通过")
}

// verifySagaStepsCompleted 测试辅助函数：验证步骤完成情况
func verifySagaStepsCompleted(t *testing.T, sagaID string, expectedSteps int) {
	t.Logf("验证步骤完成情况: SagaId=%s, 期望步骤数=%d", sagaID, expectedSteps)

	// 统计实际步骤数
	actualSteps, err := dao.SagaSteps.CountBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("统计步骤数失败: %v", err)
	}

	if actualSteps != expectedSteps {
		t.Errorf("步骤数不正确: 期望=%d, 实际=%d", expectedSteps, actualSteps)
	}

	t.Log("步骤完成情况验证通过")
}

// contains 检查字符串是否包含子字符串的辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			func() bool {
				for i := 0; i <= len(s)-len(substr); i++ {
					if s[i:i+len(substr)] == substr {
						return true
					}
				}
				return false
			}())))
}

// verifySagaTransactionStatus 验证事务状态
func verifySagaTransactionStatus(t *testing.T, sagaID string, expectedStatus string) {
	t.Logf("验证事务状态: SagaId=%s, 期望状态=%s", sagaID, expectedStatus)

	// 查询事务状态
	result, err := dao.SagaTransactions.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询事务状态失败: %v", err)
	}

	if result.SagaStatus != expectedStatus {
		t.Errorf("事务状态不正确: 期望=%s, 实际=%s", expectedStatus, result.SagaStatus)
	}

	t.Log("事务状态验证通过")
}

// verifySagaStepCreated 验证步骤是否正确创建
func verifySagaStepCreated(t *testing.T, sagaID string, expectedAction string, expectedServiceName string, expectedStepIndex int) {
	t.Logf("验证步骤创建: SagaId=%s, 期望动作=%s, 期望服务=%s, 期望索引=%d",
		sagaID, expectedAction, expectedServiceName, expectedStepIndex)

	// 查询步骤
	steps, err := dao.SagaSteps.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询步骤失败: %v", err)
	}

	// 查找匹配的步骤
	var foundStep *entity.SagaSteps
	for _, step := range steps {
		if step.StepIndex == expectedStepIndex {
			foundStep = step
			break
		}
	}

	if foundStep == nil {
		t.Fatalf("未找到步骤索引为%d的步骤", expectedStepIndex)
	}

	if foundStep.Action != expectedAction {
		t.Errorf("步骤动作不正确: 期望=%s, 实际=%s", expectedAction, foundStep.Action)
	}

	if foundStep.ServiceName != expectedServiceName {
		t.Errorf("步骤服务名不正确: 期望=%s, 实际=%s", expectedServiceName, foundStep.ServiceName)
	}

	t.Log("步骤创建验证通过")
}

// verifySagaStepIndexIncremented 验证步骤索引是否正确递增
func verifySagaStepIndexIncremented(t *testing.T, sagaID string, expectedStepIndex int) {
	t.Logf("验证步骤索引递增: SagaId=%s, 期望索引=%d", sagaID, expectedStepIndex)

	// 查询事务
	result, err := dao.SagaTransactions.FindBySagaId(context.Background(), sagaID)
	if err != nil {
		t.Fatalf("查询事务失败: %v", err)
	}

	if result.CurStepIndex != expectedStepIndex {
		t.Errorf("当前步骤索引不正确: 期望=%d, 实际=%d", expectedStepIndex, result.CurStepIndex)
	}

	t.Log("步骤索引递增验证通过")
}
