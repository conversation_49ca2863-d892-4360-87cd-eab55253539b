package service

import (
	"context"
	"errors"
	"fmt"
	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"strings"
	"testing"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/guid"
)

// TestHttpErrorClassification 测试HTTP错误分类的完整性
func TestHttpErrorClassification(t *testing.T) {
	service := NewSagaTransactions()

	// 测试所有主要HTTP状态码范围
	statusCodes := []int{
		100, 101, 102, // 1xx Informational
		200, 201, 202, 204, 206, // 2xx Success
		300, 301, 302, 304, 307, // 3xx Redirection
		400, 401, 403, 404, 405, 408, 409, 410, 429, // 4xx Client Error
		500, 501, 502, 503, 504, 505, // 5xx Server Error
		600, 700, 999, // Non-standard codes
	}

	for _, code := range statusCodes {
		t.Run(fmt.Sprintf("StatusCode_%d", code), func(t *testing.T) {
			isRetryable, errorType := service.isRetryableHTTPError(code)

			// 验证返回值的合理性
			if errorType == "" {
				t.Errorf("状态码 %d: 错误类型描述不能为空", code)
			}

			// 验证重试逻辑的合理性
			switch {
			case code >= 200 && code < 300:
				if isRetryable {
					t.Errorf("状态码 %d: 成功状态码不应该重试", code)
				}
			case code == 408 || code == 429:
				if !isRetryable {
					t.Errorf("状态码 %d: 应该可以重试", code)
				}
			case code >= 400 && code < 500:
				if code != 408 && code != 429 && isRetryable {
					t.Errorf("状态码 %d: 4xx错误（除408,429外）不应该重试", code)
				}
			case code >= 500 && code < 600:
				if code != 501 && code != 505 && !isRetryable {
					t.Errorf("状态码 %d: 5xx错误（除501,505外）应该可以重试", code)
				}
			}
		})
	}
}

// TestIsRetryableError 测试统一的重试决策函数
func TestIsRetryableError(t *testing.T) {
	service := NewSagaTransactions()

	testCases := []struct {
		name            string
		err             error
		statusCode      int
		expectedRetry   bool
		expectedContain string // 期望错误描述包含的字符串
	}{
		// 网络错误测试
		{
			name:            "网络错误-连接拒绝",
			err:             errors.New("connection refused"),
			statusCode:      0,
			expectedRetry:   true,
			expectedContain: "网络错误",
		},
		{
			name:            "网络错误-超时",
			err:             errors.New("timeout"),
			statusCode:      0,
			expectedRetry:   true,
			expectedContain: "网络错误",
		},
		{
			name:            "非网络错误",
			err:             errors.New("some other error"),
			statusCode:      0,
			expectedRetry:   false,
			expectedContain: "非网络错误",
		},

		// HTTP状态码测试
		{
			name:            "HTTP成功200",
			err:             nil,
			statusCode:      200,
			expectedRetry:   false,
			expectedContain: "成功",
		},
		{
			name:            "HTTP客户端错误400",
			err:             nil,
			statusCode:      400,
			expectedRetry:   false,
			expectedContain: "客户端错误",
		},
		{
			name:            "HTTP请求超时408",
			err:             nil,
			statusCode:      408,
			expectedRetry:   true,
			expectedContain: "请求超时",
		},
		{
			name:            "HTTP请求过多429",
			err:             nil,
			statusCode:      429,
			expectedRetry:   true,
			expectedContain: "请求过多",
		},
		{
			name:            "HTTP服务器错误500",
			err:             nil,
			statusCode:      500,
			expectedRetry:   true,
			expectedContain: "服务器错误",
		},
		{
			name:            "HTTP未实现501",
			err:             nil,
			statusCode:      501,
			expectedRetry:   false,
			expectedContain: "未实现",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			isRetryable, errorType := service.isRetryableError(tc.err, tc.statusCode)

			if isRetryable != tc.expectedRetry {
				t.Errorf("期望重试=%v, 实际重试=%v", tc.expectedRetry, isRetryable)
			}

			if !strings.Contains(errorType, tc.expectedContain) {
				t.Errorf("期望错误描述包含'%s', 实际描述='%s'", tc.expectedContain, errorType)
			}
		})
	}
}

// TestCompensationErrorSaveToDatabase 测试补偿错误信息保存到数据库
func TestCompensationErrorSaveToDatabase(t *testing.T) {
	ctx := context.Background()

	// 清理函数
	sagaID := "test-saga-error-" + guid.S()[:8] // 使用更短的ID
	defer cleanupTestData(t, sagaID)

	// 1. 创建测试事务
	createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 1, consts.SagaStatusRunning)

	testCases := []struct {
		name          string
		endpoint      string
		expectedError string
		maxRetries    int
	}{
		{
			name:          "网络错误-DNS解析失败",
			endpoint:      "http://invalid-dns-name-that-does-not-exist.local/compensate",
			expectedError: "网络错误",
			maxRetries:    2,
		},
		{
			name:          "HTTP错误-服务器错误",
			endpoint:      "https://httpbin.org/status/500",
			expectedError: "HTTP错误: 状态码 500",
			maxRetries:    1,
		},
		{
			name:          "HTTP错误-客户端错误",
			endpoint:      "https://httpbin.org/status/404",
			expectedError: "HTTP错误: 状态码 404",
			maxRetries:    1,
		},
	}

	for i, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 设置测试服务的重试配置
			testService := NewSagaTransactionsWithRetryConfig(tc.maxRetries, 100*time.Millisecond, 1*time.Second, 2.0)

			// 为每个测试用例创建独立的步骤
			testStepIndex := i + 1
			createTestSagaStep(t, sagaID, fmt.Sprintf("TestAction%d", testStepIndex), "test-service", testStepIndex)

			// 查找刚创建的步骤
			testStep, err := dao.SagaSteps.FindStepByIndex(ctx, sagaID, testStepIndex)
			if err != nil {
				t.Fatalf("查找测试步骤失败: %v", err)
			}

			// 更新步骤的补偿端点到数据库
			_, err = dao.SagaSteps.Ctx(ctx).Where("step_id", testStep.StepId).Update(g.Map{
				"compensate_endpoint": tc.endpoint,
				"compensation_status": "pending",
				"last_error":          "",
				"retry_count":         0,
			})
			if err != nil {
				t.Fatalf("更新步骤补偿端点失败: %v", err)
			}

			// 执行补偿调用
			input := &model.ExecuteCompensationInput{
				StepId:    testStep.StepId,
				SagaId:    sagaID,
				StepIndex: testStepIndex,
				Timeout:   10000, // 增加到10秒超时，确保HTTP请求能正常完成
			}

			output, err := testService.ExecuteCompensation(ctx, input)
			if err != nil {
				t.Fatalf("执行补偿失败: %v", err)
			}

			// 验证补偿失败
			if output.Success {
				t.Errorf("期望补偿失败，但实际成功了")
			}

			// 验证错误信息被保存
			if !strings.Contains(output.LastError, tc.expectedError) {
				t.Errorf("期望错误信息包含'%s', 实际错误信息='%s'", tc.expectedError, output.LastError)
			}

			// 验证错误信息包含端点信息
			if !strings.Contains(output.LastError, tc.endpoint) {
				t.Errorf("期望错误信息包含端点'%s', 实际错误信息='%s'", tc.endpoint, output.LastError)
			}

			// 验证重试次数正确
			var expectedRetryCount int
			if strings.Contains(tc.expectedError, "状态码 404") {
				// 404客户端错误不重试，只有初始尝试
				expectedRetryCount = 1
			} else {
				// 网络错误和服务器错误会重试
				expectedRetryCount = tc.maxRetries + 1 // 失败时重试次数包括最后一次失败的尝试
			}
			if output.RetryCount != expectedRetryCount {
				t.Errorf("期望重试次数=%d, 实际重试次数=%d", expectedRetryCount, output.RetryCount)
			}

			// 从数据库验证数据已保存
			updatedStep, err := dao.SagaSteps.FindStepById(ctx, testStep.StepId)
			if err != nil {
				t.Fatalf("查询更新后的步骤失败: %v", err)
			}

			if updatedStep.CompensationStatus != "failed" {
				t.Errorf("期望补偿状态为'failed', 实际为'%s'", updatedStep.CompensationStatus)
			}

			if !strings.Contains(updatedStep.LastError, tc.expectedError) {
				t.Errorf("数据库中的错误信息不正确, 期望包含'%s', 实际='%s'", tc.expectedError, updatedStep.LastError)
			}

			if updatedStep.RetryCount != expectedRetryCount {
				t.Errorf("数据库中的重试次数不正确, 期望=%d, 实际=%d", expectedRetryCount, updatedStep.RetryCount)
			}

			t.Logf("✅ 测试通过: %s", tc.name)
			t.Logf("   错误信息: %s", updatedStep.LastError)
			t.Logf("   重试次数: %d", updatedStep.RetryCount)
			t.Logf("   补偿状态: %s", updatedStep.CompensationStatus)
		})
	}
}

// TestBuildDetailedErrorMessage 测试详细错误信息构建
func TestBuildDetailedErrorMessage(t *testing.T) {
	service := NewSagaTransactions()

	testCases := []struct {
		name            string
		err             error
		statusCode      int
		responseBody    string
		endpoint        string
		totalAttempts   int
		expectedContain []string
	}{
		{
			name:            "网络错误",
			err:             errors.New("connection refused"),
			statusCode:      0,
			responseBody:    "",
			endpoint:        "http://service.local/compensate",
			totalAttempts:   3,
			expectedContain: []string{"网络错误", "connection refused", "http://service.local/compensate", "尝试次数: 3"},
		},
		{
			name:            "HTTP错误-短响应",
			err:             nil,
			statusCode:      500,
			responseBody:    `{"error": "Internal Server Error"}`,
			endpoint:        "http://service.local/compensate",
			totalAttempts:   2,
			expectedContain: []string{"HTTP错误", "状态码 500", "http://service.local/compensate", "尝试次数: 2", `{"error": "Internal Server Error"}`},
		},
		{
			name:            "HTTP错误-长响应截断",
			err:             nil,
			statusCode:      400,
			responseBody:    strings.Repeat("a", 600), // 超过500字符的响应
			endpoint:        "http://service.local/compensate",
			totalAttempts:   1,
			expectedContain: []string{"HTTP错误", "状态码 400", "http://service.local/compensate", "尝试次数: 1", "..."},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			errorMsg := service.buildDetailedErrorMessage(tc.err, tc.statusCode, tc.responseBody, tc.endpoint, tc.totalAttempts)

			for _, expected := range tc.expectedContain {
				if !strings.Contains(errorMsg, expected) {
					t.Errorf("期望错误信息包含'%s', 实际错误信息='%s'", expected, errorMsg)
				}
			}

			// 验证长响应被截断
			if len(tc.responseBody) > 500 {
				if len(errorMsg) > 1000 { // 允许一些额外的元数据
					t.Errorf("错误信息太长，可能没有正确截断响应体, 长度=%d", len(errorMsg))
				}
			}

			t.Logf("✅ 错误信息: %s", errorMsg)
		})
	}
}
