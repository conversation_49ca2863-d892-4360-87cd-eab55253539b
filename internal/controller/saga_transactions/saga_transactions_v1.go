package saga_transactions

import (
	"context"

	v1 "saga/api/saga_transactions/v1"
	"saga/internal/model"
	"saga/internal/service"
)

type ControllerV1 struct {
	sagaTransactionsService *service.SagaTransactionsService
}

func NewV1() *ControllerV1 {
	return &ControllerV1{
		sagaTransactionsService: service.NewSagaTransactions(),
	}
}

// CreateSagaTransaction 创建分布式事务
func (c *ControllerV1) CreateSagaTransaction(ctx context.Context, req *v1.CreateSagaTransactionReq) (res *v1.CreateSagaTransactionRes, err error) {
	// 构建 Service 输入参数
	input := &model.CreateSagaTransactionInput{
		Name:          req.Name,
		StepIndexMode: req.StepIndexMode,
	}

	// 转换步骤模板
	if len(req.StepTemplates) > 0 {
		for _, template := range req.StepTemplates {
			input.StepTemplates = append(input.StepTemplates, model.SagaStepTemplate{
				StepIndex:   template.StepIndex,
				Service:     template.Service,
				Action:      template.Action,
				Description: template.Description,
			})
		}
	}

	// 调用 Service 层
	output, err := c.sagaTransactionsService.CreateSagaTransaction(ctx, input)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &v1.CreateSagaTransactionRes{
		SagaId:    output.SagaId,
		Name:      output.Name,
		Status:    output.Status,
		CreatedAt: output.CreatedAt,
	}

	return res, nil
}

// ReportCompensation 上报补偿操作结果
func (c *ControllerV1) ReportCompensation(ctx context.Context, req *v1.ReportCompensationReq) (res *v1.ReportCompensationRes, err error) {
	// 构建 Service 输入参数
	input := &model.ReportCompensationInput{
		SagaId:              req.SagaId,
		Action:              req.Action,
		ServiceName:         req.ServiceName,
		ContextData:         req.ContextData,
		CompensationContext: req.CompensationContext,
		CompensateEndpoint:  req.CompensateEndpoint,
	}

	// 调用 Service 层
	output, err := c.sagaTransactionsService.ReportCompensation(ctx, input)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &v1.ReportCompensationRes{
		Success: output.Success,
	}

	return res, nil
}

// GetSagaTransaction 获取Saga事务信息
func (c *ControllerV1) GetSagaTransaction(ctx context.Context, req *v1.GetSagaTransactionReq) (res *v1.GetSagaTransactionRes, err error) {
	// 构建 Service 输入参数
	input := &model.GetSagaTransactionInput{
		SagaId: req.SagaId,
	}

	// 调用 Service 层
	output, err := c.sagaTransactionsService.GetSagaTransaction(ctx, input)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &v1.GetSagaTransactionRes{
		SagaId:      output.SagaId,
		Name:        output.Name,
		Status:      output.Status,
		CurrentStep: output.CurrentStep,
		RetryCount:  output.RetryCount,
		CreatedAt:   output.CreatedAt,
		UpdatedAt:   output.UpdatedAt,
	}

	return res, nil
}

// CommitSagaTransaction 提交分布式事务
func (c *ControllerV1) CommitSagaTransaction(ctx context.Context, req *v1.CommitSagaTransactionReq) (res *v1.CommitSagaTransactionRes, err error) {
	// 构建 Service 输入参数
	input := &model.CommitSagaTransactionInput{
		SagaId: req.SagaId,
	}

	// 调用 Service 层
	output, err := c.sagaTransactionsService.CommitSagaTransaction(ctx, input)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &v1.CommitSagaTransactionRes{
		Success:        output.Success,
		Message:        output.Message,
		SagaId:         output.SagaId,
		CompletedSteps: output.CompletedSteps,
		ExpectedSteps:  output.ExpectedSteps,
		NewStatus:      output.NewStatus,
		CompletedAt:    output.CompletedAt,
	}

	return res, nil
}

// RollBackSagaTransaction 回滚分布式事务
func (c *ControllerV1) RollBackSagaTransaction(ctx context.Context, req *v1.RollBackSagaTransactionReq) (res *v1.RollBackSagaTransactionRes, err error) {
	// 构建 Service 输入参数
	input := &model.RollbackSagaTransactionInput{
		SagaId:        req.SagaId,
		FailReason:    req.FailReason,
		FailedStep:    req.FailedStep,
		ExecutionMode: req.ExecutionMode,
	}

	if input.ExecutionMode == "" {
		input.ExecutionMode = "none" // 默认不执行
	}

	// 调用 Service 层
	output, err := c.sagaTransactionsService.RollbackSagaTransaction(ctx, input)
	if err != nil {
		return nil, err
	}

	// 构建响应
	res = &v1.RollBackSagaTransactionRes{
		Success:             output.Success,
		Message:             output.Message,
		SagaId:              output.SagaId,
		NewStatus:           output.NewStatus,
		IsRollbackCompleted: output.IsRollbackCompleted,
		StartedAt:           output.StartedAt,
		CompletedAt:         output.CompletedAt,
		FailReason:          output.FailReason,
	}

	return res, nil
}
