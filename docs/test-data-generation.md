# Saga 分布式事务系统 - 测试数据生成指南

## 🎯 概述

基于性能测试计划，我们提供了完整的测试数据生成方案，支持从小规模验证到大规模性能测试的各种场景。

## 📊 数据规模分级

根据性能测试计划，我们提供了三个测试级别：

| 级别 | Saga事务数 | 步骤记录数 | 适用场景 | 预计生成时间 |
|------|-----------|-----------|----------|-------------|
| **Level 1** | 10万 | 30万 | 基础压测、功能验证 | 2-5分钟 |
| **Level 2** | 100万 | 300万 | 中等压测、性能调优 | 20-30分钟 |
| **Level 3** | 1000万 | 3000万 | 极限压测、容量规划 | 2-4小时 |
| **Quick Test** | 100 | 300 | 开发验证、接口测试 | 1-2秒 |

## 🚀 快速开始

### 1. 初始化测试数据脚本
```bash
# 首次使用需要初始化脚本
make db-init-perf-data
```

### 2. 生成快速测试数据
```bash
# 生成100条测试数据，用于开发验证
make db-quick-test
```

### 3. 生成性能测试数据
```bash
# Level 1: 10万条数据
make db-generate-level1

# Level 2: 100万条数据
make db-generate-level2

# Level 3: 1000万条数据（需要较长时间）
make db-generate-level3
```

### 4. 自定义数据量
```bash
# 生成指定数量的数据
make db-generate-custom COUNT=50000
```

### 5. 查看数据统计
```bash
# 显示详细的数据统计信息
make db-show-stats
```

### 6. 清理测试数据
```bash
# 清理所有测试数据
make db-clean-test-data
```

## 🏢 业务场景

测试数据包含5种真实的业务场景，更贴近实际使用情况：

### 1. 电商订单处理流程
```
步骤流程：
1. 用户验证 (user-service)
2. 库存预留 (inventory-service)  
3. 创建订单 (order-service)
4. 处理支付 (payment-service)
5. 创建配送 (logistics-service)
```

### 2. 金融转账处理流程
```
步骤流程：
1. 账户验证 (account-service)
2. 风险评估 (risk-service)
3. 冻结金额 (account-service)
4. 执行转账 (transfer-service)
5. 发送通知 (notification-service)
```

### 3. 酒店预订处理流程
```
步骤流程：
1. 检查可用性 (hotel-service)
2. 创建预订 (booking-service)
3. 处理支付 (payment-service)
4. 发送确认 (notification-service)
```

### 4. 保险理赔处理流程
```
步骤流程：
1. 理赔验证 (claim-service)
2. 损失评估 (assessment-service)
3. 审批支付 (approval-service)
4. 执行赔付 (payment-service)
5. 更新记录 (record-service)
```

### 5. 通用业务流程
```
步骤流程：
1. 请求验证 (validation-service)
2. 业务处理 (business-service)
3. 发送通知 (notification-service)
```

## 📈 数据特征

### Saga 事务特征
- **状态分布**: pending(20%), running(30%), completed(30%), compensating(10%), failed(10%)
- **索引模式**: auto(70%), manual(30%)
- **补偿窗口**: 300秒（金融600秒，保险1800秒）
- **时间分布**: 随机分布在过去7天内

### 步骤特征
- **补偿状态**: 根据Saga状态智能分配
- **重试次数**: 0-3次随机分布
- **上下文数据**: 包含真实的JSON格式业务数据
- **补偿端点**: 符合实际URL格式

## 🔍 性能优化

### 索引优化
生成的数据会自动创建以下性能优化索引：

```sql
-- Saga 事务索引
CREATE INDEX idx_saga_status ON saga_transactions(saga_status);
CREATE INDEX idx_saga_created_at ON saga_transactions(created_at);
CREATE INDEX idx_saga_step_mode ON saga_transactions(step_index_mode);

-- 步骤索引
CREATE INDEX idx_step_compensation_status ON saga_steps(compensation_status);
CREATE INDEX idx_step_saga_compensation ON saga_steps(saga_id, compensation_status);
CREATE INDEX idx_compensation_processing ON saga_steps(compensation_status, saga_id, step_index);
```

### 生成优化
- **批量提交**: 每1000条记录提交一次事务
- **进度显示**: 大数据量生成时显示进度
- **内存优化**: 避免长事务导致的内存问题

## 📊 数据统计示例

```sql
-- 查看数据统计
CALL ShowDataStatistics();

-- 输出示例：
=== Saga 事务统计 ===
total_sagas: 100000
pending: 20000, running: 30000, completed: 30000
compensating: 10000, failed: 10000
auto_mode: 70000, manual_mode: 30000

=== 步骤统计 ===  
total_steps: 300000
pending: 45000, running: 15000, completed: 180000
failed: 30000, delay: 30000

=== 服务分布统计 ===
payment-service: 85000 steps
order-service: 65000 steps
notification-service: 75000 steps
...
```

## ⚠️ 注意事项

### 磁盘空间要求
- **Level 1**: ~500MB
- **Level 2**: ~5GB  
- **Level 3**: ~50GB

### 生成时间预估
- **Level 1**: 2-5分钟
- **Level 2**: 20-30分钟
- **Level 3**: 2-4小时（取决于硬件性能）

### 最佳实践
1. **逐级测试**: 先从Level 1开始，确认无问题后再生成更大数据量
2. **监控资源**: 生成大数据量时监控磁盘空间和内存使用
3. **备份策略**: 重要数据请提前备份
4. **清理及时**: 测试完成后及时清理数据

## 🛠 故障排除

### 常见问题

1. **生成中断**
   ```bash
   # 检查数据库连接
   make db-logs
   
   # 重新初始化
   make db-reset
   make db-init-test-data
   ```

2. **磁盘空间不足**
   ```bash
   # 清理测试数据
   make db-clean-test-data
   
   # 检查磁盘空间
   df -h
   ```

3. **生成速度慢**
   ```bash
   # 检查数据库配置
   make db-shell
   SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
   
   # 使用性能配置启动数据库
   make db-start  # 使用性能配置
   ```

## 🎯 使用场景

### 开发阶段
```bash
# 快速验证功能
make db-quick-test
```

### 测试阶段  
```bash
# 功能测试
make db-generate-level1

# 性能测试
make db-generate-level2
```

### 生产准备
```bash
# 容量规划
make db-generate-level3
```

通过这套完整的测试数据生成方案，可以有效支撑 Saga 分布式事务系统的各种测试需求，为系统优化和生产部署提供可靠的数据基础。
