# Saga 补偿处理服务

## 概述

补偿处理服务是 Saga 分布式事务框架的核心组件，负责处理所有未完成的补偿操作。该服务不仅处理服务重启后的中断恢复场景，还包括超时重试、延期上报等各种未完成补偿任务的处理，确保分布式事务的最终一致性。

**重要说明：该服务完全自动化运行，无需任何外部API调用或手动干预。**

## 核心功能

### 1. 自动处理机制
- **服务启动时自动处理**：服务启动后立即检测并处理未完成的补偿任务
- **定时检查**：按设定间隔（默认30秒）定时检查是否有需要处理的任务
- **超时任务重置**：自动重置超时的运行中任务（默认5分钟），避免任务永久卡死
- **多场景覆盖**：处理中断恢复、超时重试、延期上报等各种场景

### 2. 任务状态管理
- **uninitialized**：未初始化的补偿任务
- **pending**：待处理的补偿任务
- **running**：正在执行的补偿任务
- **completed**：已完成的补偿任务
- **failed**：执行失败的补偿任务
- **delay**：延期上报的补偿任务

### 3. 并发控制
- **信号量机制**：限制并发执行的补偿任务数量（默认10个）
- **按 Saga 分组**：每个 Saga 的补偿任务串行执行，保证执行顺序
- **逆序执行**：补偿任务按 step_index 逆序执行
- **资源管理**：避免过多并发导致系统资源耗尽

### 4. 处理场景覆盖
- **中断恢复**：服务重启后自动恢复未完成的补偿任务
- **超时重试**：自动重试超时的补偿任务
- **延期处理**：处理延期上报状态的补偿任务
- **状态同步**：确保补偿完成后 Saga 状态的正确更新

## 服务配置

### 默认配置
```go
taskTimeout: 5 * time.Minute     // 任务超时时间
processingInterval: 30 * time.Second // 处理检查间隔
maxConcurrentTasks: 10           // 最大并发任务数
```

### 配置参数说明
- `taskTimeout`：单个补偿任务的超时时间，超过此时间的 running 任务会被重置为 pending
- `processingInterval`：自动处理检查的间隔时间
- `maxConcurrentTasks`：同时执行的补偿任务的最大数量

### 配置修改
可以通过 `SetConfig` 方法修改服务配置：
```go
processorService := service.NewCompensationProcessorService()
processorService.SetConfig(
    10*time.Minute,  // 任务超时时间
    60*time.Second,  // 处理检查间隔
    20,              // 最大并发任务数
)
```

## 服务启动

### 自动启动
补偿处理服务在应用启动时自动启动：
```go
// 在 cmd.go 中自动启动
processorService := service.NewCompensationProcessorService()
err = processorService.StartProcessing(ctx)
if err != nil {
    g.Log().Errorf(ctx, "启动补偿处理服务失败: %v", err)
    return err
}
g.Log().Infof(ctx, "补偿处理服务已启动")
```

### 手动控制
虽然服务自动运行，但也提供了手动控制方法：
```go
// 启动处理服务
err := processorService.StartProcessing(ctx)

// 停止处理服务
err := processorService.StopProcessing(ctx)

// 获取服务状态
status, err := processorService.GetProcessingStatus(ctx)
```



## 处理机制详解

### 1. 服务启动时的自动处理
```go
// 在服务启动时自动开始处理
func (s *CompensationProcessorService) StartProcessing(ctx context.Context) error {
    // 1. 立即执行一次处理
    go s.runProcessing(ctx)

    // 2. 启动定时处理任务
    go s.startPeriodicProcessing(ctx)

    return nil
}
```

### 2. 处理流程
1. **查找未完成任务**：查询数据库中状态为 `pending`、`running`、`delay` 的补偿任务
2. **重置超时任务**：将长时间处于 `running` 状态的任务重置为 `pending`
3. **按 Saga 分组**：将任务按 `sagaId` 分组，确保同一 Saga 的任务串行执行
4. **逆序排序**：每个 Saga 的补偿任务按 `step_index` 逆序执行
5. **并发执行**：在配置的并发限制内，并发处理不同 Saga 的补偿任务
6. **状态更新**：根据执行结果更新任务状态和 Saga 状态
7. **完成检查**：检查所有补偿是否完成，自动更新 Saga 最终状态

### 3. 错误处理
- **网络错误**：支持自动重试，遵循指数退避策略
- **业务错误**：记录详细错误信息，便于排查问题
- **超时错误**：自动重置任务状态，避免任务永久卡死

### 4. 补偿任务执行
```go
// 补偿任务执行流程
func (s *CompensationProcessorService) executeCompensationTask(ctx context.Context, task *entity.SagaSteps) bool {
    // 1. 检查 Saga 状态
    // 2. 调用现有的 ExecuteCompensation 方法
    // 3. 处理执行结果
    // 4. 更新任务状态
}
```

### 5. 多场景处理能力
- **中断恢复场景**：服务重启后自动检测并恢复未完成的补偿任务
- **超时重试场景**：自动重置超时的 running 状态任务，重新执行
- **延期处理场景**：处理 delay 状态的补偿任务，支持延期上报机制
- **状态同步场景**：补偿完成后自动检查并更新 Saga 最终状态

## 监控和告警

### 1. 关键指标
- **未完成任务数**：当前系统中未完成的补偿任务数量（包括 pending、running、delay）
- **处理成功率**：补偿任务处理的成功率
- **平均处理时间**：每次处理任务的平均耗时
- **超时任务数**：超过设定时间仍未完成的任务数
- **延期任务数**：处于 delay 状态的补偿任务数量

### 2. 日志监控
补偿处理服务会输出详细的日志信息：
```
2024-01-15T10:30:00Z [INFO] 启动补偿处理服务: 任务超时=5m0s, 检查间隔=30s, 最大并发=10
2024-01-15T10:30:00Z [INFO] 开始执行补偿处理任务
2024-01-15T10:30:05Z [INFO] 发现 3 个未完成的补偿任务
2024-01-15T10:30:05Z [INFO] 开始处理 Saga saga-001 的补偿任务，共 2 个
2024-01-15T10:30:10Z [INFO] 处理补偿任务: SagaId=saga-001, StepId=step-001, StepIndex=2, Action=ProcessPayment
2024-01-15T10:30:15Z [INFO] Saga saga-001 补偿任务处理完成: 成功=2, 失败=0
2024-01-15T10:30:16Z [INFO] 所有补偿动作已完成，Saga状态已更新为failed: SagaId=saga-001
```

### 3. 告警建议
- **未完成任务过多**：当未完成任务数超过阈值时触发告警
- **处理成功率低**：当处理成功率低于阈值时触发告警
- **处理时间过长**：当处理时间过长时触发告警
- **延期任务积压**：当 delay 状态任务过多时触发告警
- **服务停止**：当处理服务意外停止时触发告警

### 4. 监控 SQL 查询
```sql
-- 查询未完成的补偿任务
SELECT saga_id, step_id, action, service_name, compensation_status, last_error, retry_count, created_at, updated_at
FROM saga_steps
WHERE compensation_status IN ('pending', 'running', 'delay')
ORDER BY created_at;

-- 统计各状态的补偿任务数量
SELECT compensation_status, COUNT(*) as count
FROM saga_steps
GROUP BY compensation_status;

-- 查找超时的运行中任务
SELECT saga_id, step_id, action, service_name, updated_at
FROM saga_steps 
WHERE compensation_status = 'running' 
AND updated_at < NOW() - INTERVAL 5 MINUTE;

-- 统计未完成补偿任务的数量
SELECT
    COUNT(*) as total_unfinished,
    SUM(CASE WHEN compensation_status = 'pending' THEN 1 ELSE 0 END) as pending_count,
    SUM(CASE WHEN compensation_status = 'running' THEN 1 ELSE 0 END) as running_count,
    SUM(CASE WHEN compensation_status = 'delay' THEN 1 ELSE 0 END) as delay_count
FROM saga_steps
WHERE compensation_status IN ('pending', 'running', 'delay');
```

## 数据库索引优化

为了提高补偿恢复服务的查询性能，建议创建以下索引：

```sql
-- 补偿状态索引
CREATE INDEX IF NOT EXISTS idx_saga_steps_compensation_status ON saga_steps(compensation_status);

-- 补偿状态和时间复合索引
CREATE INDEX IF NOT EXISTS idx_saga_steps_compensation_status_time ON saga_steps(compensation_status, updated_at);

-- SagaId 和补偿状态索引
CREATE INDEX IF NOT EXISTS idx_saga_steps_saga_compensation ON saga_steps(saga_id, compensation_status);

-- 创建时间索引
CREATE INDEX IF NOT EXISTS idx_saga_steps_created_at ON saga_steps(created_at);

-- 更新时间索引
CREATE INDEX IF NOT EXISTS idx_saga_steps_updated_at ON saga_steps(updated_at);

-- 步骤索引
CREATE INDEX IF NOT EXISTS idx_saga_steps_step_index ON saga_steps(step_index);

-- 复合索引用于补偿恢复查询
CREATE INDEX IF NOT EXISTS idx_saga_steps_compensation_recovery ON saga_steps(compensation_status, saga_id, step_index);
```

## 最佳实践

### 1. 服务部署
- **单点部署**：建议补偿恢复服务以单实例模式部署，避免重复执行
- **高可用**：可以配置主备模式，主实例故障时自动切换
- **资源配置**：根据补偿任务数量合理配置内存和CPU资源
- **监控覆盖**：确保恢复服务的运行状态被监控系统覆盖

### 2. 参数调优
- **任务超时时间**：根据实际业务场景设置合理的超时时间
  - 网络调用较多的场景：建议设置较长的超时时间（如10分钟）
  - 简单业务逻辑：可以设置较短的超时时间（如3分钟）
- **恢复间隔**：平衡系统负载和恢复及时性
  - 高负载场景：适当增加间隔时间（如60秒）
  - 低负载场景：可以设置较短间隔（如15秒）
- **并发数量**：根据系统资源和下游服务能力设置并发数
  - 考虑数据库连接池大小
  - 考虑下游服务的并发处理能力

### 3. 故障排查
- **查看日志**：通过日志分析恢复过程中的问题
- **监控指标**：通过监控指标识别异常情况
- **数据库查询**：直接查询数据库了解任务状态
- **服务状态检查**：检查恢复服务是否正常运行

### 4. 性能优化
- **数据库索引**：为相关字段创建合适的索引
- **批量查询**：优化数据库查询，减少网络开销
- **连接复用**：合理使用数据库连接池
- **日志级别**：生产环境适当调整日志级别

### 5. 业务集成
- **幂等性**：确保补偿接口具有幂等性
- **超时设置**：合理设置补偿接口的超时时间
- **错误处理**：补偿接口应该返回明确的错误信息
- **监控告警**：为补偿接口添加监控和告警

## 常见问题

### Q1: 服务重启后是否会重复执行已完成的补偿任务？
A1: 不会。系统会检查任务状态，只恢复状态为 `pending` 或 `running` 的任务。

### Q2: 如何处理补偿任务执行失败的情况？
A2: 系统会记录失败原因，并根据重试策略进行重试。最终失败的任务会标记为 `failed` 状态。

### Q3: 恢复服务对系统性能有什么影响？
A3: 恢复服务通过并发控制和资源管理，将对系统性能的影响降到最低。可以通过调整配置参数来平衡性能和恢复效率。

### Q4: 如何确保补偿任务不会重复执行？
A4: 每个补偿任务都有唯一的 `stepId`，业务服务应该实现幂等性检查，确保同一任务不会重复执行。

### Q5: 恢复服务本身故障怎么办？
A5: 建议配置健康检查和自动重启机制。同时，可以通过监控和告警及时发现问题。服务重启后会自动继续恢复未完成的任务。

### Q6: 如何验证恢复服务是否正常工作？
A6: 可以通过以下方式验证：
- 查看应用启动日志，确认恢复服务已启动
- 监控日志中的定时恢复任务执行情况
- 查询数据库中的未完成任务数量变化
- 通过监控指标观察恢复服务的运行状态

### Q7: 补偿任务的执行顺序如何保证？
A7: 系统会按照以下规则执行补偿任务：
- 同一 Saga 的补偿任务按 `step_index` 逆序执行
- 不同 Saga 的补偿任务可以并发执行
- 通过信号量机制控制总体并发数量

### Q8: 如何处理长时间运行的补偿任务？
A8: 对于长时间运行的补偿任务，建议：
- 适当增加任务超时时间配置
- 在补偿接口中实现进度汇报机制
- 考虑将长任务拆分为多个短任务
- 增加详细的日志记录便于跟踪

## 架构设计

### 1. 组件关系
```
应用启动 -> 补偿处理服务 -> 定时任务 -> 数据库查询 -> 补偿任务执行 -> 状态更新 -> 完成检查
```

### 2. 核心组件
- **CompensationProcessorService**：补偿处理服务主体
- **SagaTransactionsService**：Saga事务服务，提供补偿执行能力
- **数据库DAO层**：提供数据访问能力
- **并发控制器**：管理任务并发执行
- **状态检查器**：检查补偿完成情况并更新 Saga 状态

### 3. 数据流
1. 定时任务查询未完成的补偿任务（pending、running、delay）
2. 重置超时任务状态
3. 按 Saga 分组并排序任务
4. 并发执行不同 Saga 的补偿任务
5. 更新任务执行状态
6. 检查补偿完成情况
7. 自动更新 Saga 最终状态
8. 记录执行结果和错误信息

### 4. 处理场景扩展
- **中断恢复**：服务重启后的任务恢复
- **超时处理**：长时间运行任务的超时重置
- **延期处理**：delay 状态任务的延期执行
- **状态同步**：补偿完成后的状态自动更新

这个补偿处理服务是 Saga 分布式事务框架的重要组成部分，不仅确保了系统的可靠性和一致性，还扩展了对各种补偿场景的处理能力。通过自动化的处理机制，大大减少了人工干预的需求，提高了系统的可维护性和健壮性。