# Auto 模式下 ReportCompensation 高并发问题测试报告

## 测试概述

本报告记录了在 service 层对 `ReportCompensation` 方法进行的高并发测试，特别关注在 auto 模式下相同 `sagaId + serviceName + Action` 的处理情况。

## 测试环境

- **测试文件**: `internal/service/saga_transactions_concurrent_test.go`
- **测试方法**: `TestReportCompensation_ConcurrentSameActionAutoMode`
- **数据库**: MySQL
- **Go 版本**: Go 1.19+
- **测试框架**: GoFrame GTest

## 测试场景

### 场景1: 高并发相同 Action 测试

**测试参数:**
- 并发数: 20
- 相同参数: `sagaId`, `action="create_order"`, `serviceName="order-service"`
- 不同参数: `ContextData` (每个请求包含不同的 index)

**测试结果:**
```
并发测试完成: 总请求数=20, 成功数=20, 失败数=0, 耗时=1.305374048s
数据库验证: 总步骤数=1, 相同action步骤数=1
```

### 场景2: 幂等性验证测试

**测试参数:**
- 调用次数: 2
- 相同参数: `sagaId`, `action="idempotent_action"`, `serviceName="idempotent-service"`
- 不同参数: `ContextData` (第一次 vs 第二次调用)

**测试结果:**
```
最终记录: StepId=..., StepIndex=2, ContextData={"data": "second_call"}
```

## 发现的问题

### 问题1: step_index 不连续

**现象:**
- 预期: 只有1条记录时，step_index 应该为 1
- 实际: step_index 为 20 (并发测试) 或 2 (幂等性测试)

**原因分析:**
在 `createOrUpdateStep` 方法中，每次调用都会基于当前的 `CurStepIndex` 分配新的 `step_index`:

```go
// 问题代码
stepIndex = sagaTransaction.CurStepIndex + 1
```

这导致即使是相同的 `action + service_name` 组合，每次调用都会获得递增的 `step_index`。

### 问题2: CurStepIndex 不正确

**现象:**
- 预期: 只有1条记录时，CurStepIndex 应该为 1
- 实际: CurStepIndex 为 20 (并发测试) 或 2 (幂等性测试)

**原因分析:**
每次调用 `ReportCompensation` 都会更新 `CurStepIndex`:

```go
// 问题代码
if sagaTransaction.StepIndexMode == StepIndexModeAuto {
    updateData[dao.SagaTransactions.Columns().CurStepIndex] = stepIndex
}
```

这导致 `CurStepIndex` 与实际的步骤数量不一致。

### 问题3: 违反幂等性原则

**现象:**
- 相同的 `sagaId + serviceName + Action` 应该是幂等的
- 但是每次调用都会分配新的 `step_index`
- 数据库记录虽然只有1条（由于唯一约束），但 `step_index` 不断变化

## 测试日志分析

### 并发测试日志片段

```
2025-07-16T02:46:03.825+08:00 [INFO] 使用auto模式 StepIndex: SagaId=d5osy60k9f0dbcuoh47ar6o1001e5agt, Action=create_order, 当前CurStepIndex=0, 新StepIndex=1

2025-07-16T02:46:03.874+08:00 [INFO] 使用auto模式 StepIndex: SagaId=d5osy60k9f0dbcuoh47ar6o1001e5agt, Action=create_order, 当前CurStepIndex=1, 新StepIndex=2

2025-07-16T02:46:03.914+08:00 [INFO] 使用auto模式 StepIndex: SagaId=d5osy60k9f0dbcuoh47ar6o1001e5agt, Action=create_order, 当前CurStepIndex=2, 新StepIndex=3

...

2025-07-16T02:46:04.934+08:00 [INFO] 使用auto模式 StepIndex: SagaId=d5osy60k9f0dbcuoh47ar6o1001e5agt, Action=create_order, 当前CurStepIndex=19, 新StepIndex=20
```

**观察:**
- 20个并发请求依次获得了 step_index 1-20
- 每个请求都认为自己是新的步骤
- 实际上应该是相同的步骤（相同的 action + service_name）

## 预期行为 vs 实际行为

### 预期行为 (正确的幂等性)

1. **第一次调用**: 创建新记录，step_index=1，CurStepIndex=1
2. **后续调用**: 更新现有记录，step_index保持=1，CurStepIndex保持=1
3. **最终状态**: 只有1条记录，step_index=1，CurStepIndex=1

### 实际行为 (当前问题)

1. **第一次调用**: 创建新记录，step_index=1，CurStepIndex=1
2. **第二次调用**: 更新现有记录，step_index=2，CurStepIndex=2
3. **第N次调用**: 更新现有记录，step_index=N，CurStepIndex=N
4. **最终状态**: 只有1条记录，step_index=N，CurStepIndex=N

## 影响分析

### 1. 数据一致性问题
- `CurStepIndex` 不能准确反映实际步骤数量
- 可能影响补偿逻辑的执行顺序

### 2. 业务逻辑问题
- 违反了幂等性原则
- 可能导致错误的步骤计数

### 3. 性能问题
- 不必要的数据库更新操作
- 事务锁定时间过长

## 建议的解决方案

### 方案1: 修复 step_index 分配逻辑

```go
// 改进方案：在 auto 模式下，先检查是否已存在相同的 action + service_name
if sagaTransaction.StepIndexMode == StepIndexModeAuto {
    // 检查是否已存在相同的步骤
    existingStep, err := dao.SagaSteps.FindByActionAndService(ctx, input.SagaId, input.Action, input.ServiceName)
    if err != nil {
        return nil, err
    }
    
    if existingStep != nil {
        // 使用现有步骤的 step_index
        stepIndex = existingStep.StepIndex
    } else {
        // 分配新的 step_index
        stepIndex = sagaTransaction.CurStepIndex + 1
    }
}
```

### 方案2: 改进 CurStepIndex 更新逻辑

```go
// 只有在创建新步骤时才更新 CurStepIndex
if sagaTransaction.StepIndexMode == StepIndexModeAuto && existingStep == nil {
    updateData[dao.SagaTransactions.Columns().CurStepIndex] = stepIndex
}
```

## 测试建议

1. **添加更多幂等性测试**
2. **测试不同 action 的并发处理**
3. **验证 step_index 的唯一性和连续性**
4. **测试 CurStepIndex 的准确性**

## 结论

测试成功揭示了在 auto 模式下 `ReportCompensation` 方法的关键问题：

1. ✅ **并发安全性**: 数据库唯一约束确保了只有一条记录
2. ❌ **幂等性**: step_index 不断递增，违反了幂等性原则
3. ❌ **数据一致性**: CurStepIndex 与实际步骤数量不一致
4. ❌ **业务逻辑**: 相同的 action + service_name 应该保持相同的 step_index

这些问题需要在业务逻辑层面进行修复，以确保 auto 模式下的正确行为。 