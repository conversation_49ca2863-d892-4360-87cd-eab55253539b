# Saga 分布式事务服务使用指南

## 概述

本文档介绍如何使用 Saga 分布式事务服务来创建和管理分布式事务。

## 状态枚举

### Saga 事务状态
- `pending`: 待处理（默认状态）
- `running`: 运行中
- `completed`: 已完成
- `compensating`: 补偿中
- `failed`: 已失败
- `compensated` : 补偿完成

### 步骤状态
- `pending`: 待执行
- `running`: 执行中
- `completed`: 已完成
- `failed`: 执行失败
- `compensated`: 已补偿

### 补偿状态
- `pending`: 待补偿
- `running`: 补偿中
- `completed`: 补偿完成
- `failed`: 补偿失败

## 创建分布式事务

### 请求示例

```go
package main

import (
    "context"
    "sage/internal/service"
)

func main() {
    ctx := context.Background()
    sagaService := &service.SagaTransactionsService{}
    
    // 创建 Saga 事务请求
    req := &service.CreateSagaTransactionReq{
        Name: "订单支付流程",
        Steps: []service.SagaStep{
            {
                Name:            "创建订单",
                ServiceName:     "order-service",
                CompensateAction: "cancel_order",
                TimeoutSec:      30,
                RetryCount:      3,
            },
            {
                Name:            "扣减库存",
                ServiceName:     "inventory-service", 
                CompensateAction: "restore_inventory",
                TimeoutSec:      30,
                RetryCount:      3,
            },
            {
                Name:            "处理支付",
                ServiceName:     "payment-service",
                CompensateAction: "refund_payment",
                TimeoutSec:      60,
                RetryCount:      2,
            },
        },
        Context: map[string]interface{}{
            "orderId":   "12345",
            "userId":    "user123",
            "amount":    100.0,
            "currency":  "CNY",
        },
        TimeoutSec: 300, // 5分钟总超时
    }
    
    // 创建事务
    res, err := sagaService.CreateSagaTransaction(ctx, req)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("创建成功，SagaId: %s, Status: %s\n", res.SagaId, res.Status)
}
```

### 上报步骤执行结果示例

```go
// 服务执行正向操作后上报结果
func (s *OrderService) ExecuteCreateOrder(ctx context.Context, sagaId string) error {
    // 执行创建订单操作
    err := s.createOrder(ctx)
    
    // 上报执行结果
    status := "completed"
    if err != nil {
        status = "failed"
    }
    
    return s.ReportStepResult(ctx, sagaId, "创建订单", status)
}
```

### 上报补偿操作结果示例

```go
// 服务执行补偿操作后上报结果
func (s *OrderService) ExecuteCompensateOrder(ctx context.Context, sagaId string) error {
    // 执行取消订单操作
    err := s.cancelOrder(ctx)
    
    // 上报补偿结果
    status := "completed"
    if err != nil {
        status = "failed"
    }
    
    return s.ReportCompensationResult(ctx, sagaId, "创建订单", "cancel_order", status)
}
```

### 响应示例

```json
{
    "sagaId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "pending"
}
```

## 工作流程

### 1. 创建阶段
- Orchestrator 创建 Saga 事务记录
- 状态设置为 `pending`
- 发布 `saga_created` 事件

### 2. 执行阶段
- 各服务监听 `saga_created` 事件
- 服务根据自己的步骤配置，**自行决定何时启动正向操作**
- 服务执行完成后，上报执行结果给 Orchestrator

### 3. 状态追踪
- Orchestrator 接收服务上报的状态
- 更新 Saga 状态和当前步骤
- 判断是否需要进入补偿阶段

### 4. 补偿控制
- 如果某个步骤失败，Orchestrator 启动补偿流程
- 状态变更为 `compensating`
- 发布补偿事件，各服务自行执行补偿动作
- **各服务执行补偿操作后，主动上报补偿结果**
- Orchestrator 根据补偿结果决定最终状态

### 流程图示例

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Orchestrator as Orchestrator
    participant OrderService as 订单服务
    participant PaymentService as 支付服务
    participant InventoryService as 库存服务
    participant EventBus as 事件总线

    Client->>Orchestrator: 创建 Saga 事务
    Orchestrator->>Orchestrator: 生成 SagaId
    Orchestrator->>EventBus: 发布 saga_created 事件
    
    EventBus->>OrderService: 接收 saga_created 事件
    OrderService->>OrderService: 执行创建订单操作
    OrderService->>Orchestrator: 上报步骤执行结果 (completed)
    
    EventBus->>PaymentService: 接收 saga_created 事件
    PaymentService->>PaymentService: 执行支付操作
    PaymentService->>Orchestrator: 上报步骤执行结果 (failed)
    
    Orchestrator->>Orchestrator: 更新状态为 compensating
    Orchestrator->>EventBus: 发布 saga_compensation_requested 事件
    
    EventBus->>OrderService: 接收补偿事件
    OrderService->>OrderService: 执行取消订单操作
    OrderService->>Orchestrator: 上报补偿操作结果 (completed)
    
    Orchestrator->>Orchestrator: 更新最终状态为 failed
    Orchestrator->>Client: 返回最终结果
```

## 服务集成指南

### 服务端实现

各微服务需要实现以下接口：

```go
// 监听 Saga 事件
func (s *YourService) HandleSagaEvent(ctx context.Context, event SagaEvent) error {
    switch event.EventType {
    case "saga_created":
        // 检查是否有属于自己的步骤
        if s.hasMyStep(event.Steps) {
            // 启动正向操作
            return s.startForwardOperation(ctx, event)
        }
    case "saga_compensation_requested":
        // 执行补偿操作
        return s.compensate(ctx, event)
    }
    return nil
}

// 上报步骤执行结果
func (s *YourService) ReportStepResult(ctx context.Context, sagaId string, stepName string, status string) error {
    req := &service.ReportStepStatusReq{
        SagaId:      sagaId,
        StepName:    stepName,
        ServiceName: s.ServiceName,
        Status:      status,
        Message:     "步骤执行完成",
        ExecutedAt:  time.Now().Unix(),
    }
    return sagaService.ReportStepStatus(ctx, req)
}

// 上报补偿操作结果
func (s *YourService) ReportCompensationResult(ctx context.Context, sagaId string, stepName string, compensateAction string, status string) error {
    req := &service.ReportCompensationReq{
        SagaId:           sagaId,
        StepName:         stepName,
        ServiceName:      s.ServiceName,
        CompensateAction: compensateAction,
        Status:           status,
        Message:          "补偿操作完成",
        CompensatedAt:    time.Now().Unix(),
    }
    return sagaService.ReportCompensation(ctx, req)
}
```

### 事件格式

#### Saga 创建事件
```json
{
    "eventType": "saga_created",
    "sagaId": "550e8400-e29b-41d4-a716-446655440000",
    "sagaName": "订单支付流程",
    "steps": [
        {
            "name": "创建订单",
            "serviceName": "order-service",
            "compensateAction": "cancel_order",
            "timeoutSec": 30,
            "retryCount": 3
        }
    ],
    "context": {
        "orderId": "12345",
        "userId": "user123"
    },
    "timeoutSec": 300,
    "createdAt": "2023-12-01T10:00:00Z"
}
```

#### Saga 补偿事件
```json
{
    "eventType": "saga_compensation_requested",
    "sagaId": "550e8400-e29b-41d4-a716-446655440000",
    "failedStepName": "处理支付",
    "requestedAt": "2023-12-01T10:05:00Z"
}
```

## 补偿服务对接

为了让业务服务能够正确实现补偿接口，我们提供了详细的对接指南：

- **[补偿服务集成文档](compensation-service-integration.md)** - 完整的技术规范和实现示例
- **[补偿服务快速入门](compensation-service-quickstart.md)** - 5分钟快速实现补偿接口

### 核心要点

1. **HTTP接口**: 补偿接口必须使用 HTTP POST 方法
2. **状态码判断**: 通过 HTTP 200-299 状态码判断补偿成功
3. **幂等性**: 补偿操作必须支持重复调用
4. **上下文数据**: 在上报补偿信息时提供充足的上下文数据

### 集成步骤

1. 实现补偿HTTP接口
2. 在上报补偿信息时设置 `compensate_endpoint` 
3. 测试补偿接口的幂等性
4. 添加监控和日志

详细的实现方法请参考上述对接文档。

## 最佳实践

### 1. 步骤设计
- 每个步骤应该是**原子性**的
- 补偿动作必须是**幂等**的
- 设置合理的超时时间和重试次数

### 2. 状态管理
- 及时上报步骤执行状态
- **主动上报补偿操作结果**
- 处理重复消息（幂等性）
- 实现优雅的错误处理

### 3. 监控告警
- 监控 Saga 执行时间
- 告警长时间未完成的事务
- 记录详细的执行日志

### 4. 容错设计
- 实现断路器模式
- 设计降级方案
- 处理消息丢失和重复

## 注意事项

1. **非强一致性**：Saga 提供最终一致性，不是强一致性
2. **可见性**：事务执行过程中，中间状态对外可见
3. **隔离性**：需要应用层处理并发访问问题
4. **补偿复杂性**：补偿逻辑可能比正向操作更复杂
5. **主动上报**：各服务必须主动上报执行结果和补偿结果，不能依赖 Orchestrator 查询
6. **幂等性**：补偿操作必须是幂等的，能够重复执行

## 后续扩展

- [ ] 实现 Saga 步骤服务
- [ ] 集成消息队列
- [ ] 添加监控指标
- [ ] 实现管理界面
- [ ] 添加事务模板功能 