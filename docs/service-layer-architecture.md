# Service 层架构设计说明

## 设计原则

### Service 层职责
✅ **专注业务逻辑**
- 实现核心业务逻辑
- 处理业务流程编排
- 管理业务状态转换
- 调用 DAO 层进行数据操作

❌ **不负责的功能**
- 参数格式验证
- 请求参数校验
- HTTP 相关处理
- 数据格式转换

### Controller 层职责
✅ **负责的功能**
- 参数验证和格式检查
- 请求数据转换
- 响应数据格式化
- HTTP 状态码管理
- 调用 Service 层处理业务逻辑

## 分层架构图

```mermaid
graph TD
    subgraph "分层架构"
        A[Client/前端] --> B[Controller 层]
        B --> C[Service 层]
        C --> D[DAO 层]
        D --> E[Database]
    end
    
    subgraph "Controller 层职责"
        B1[参数验证]
        B2[请求格式化]
        B3[响应封装]
        B4[错误处理]
        B5[权限检查]
    end
    
    subgraph "Service 层职责"
        C1[业务逻辑]
        C2[状态管理]
        C3[流程编排]
        C4[事件发布]
        C5[事务管理]
    end
    
    subgraph "DAO 层职责"
        D1[数据持久化]
        D2[SQL 执行]
        D3[连接管理]
        D4[查询优化]
        D5[数据映射]
    end
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
    B --> B5
    
    C --> C1
    C --> C2
    C --> C3
    C --> C4
    C --> C5
    
    D --> D1
    D --> D2
    D --> D3
    D --> D4
    D --> D5
```

## 分层架构优势

### 1. **职责分离** 🎯
- **Controller 层**：专注于接入层逻辑和参数验证
- **Service 层**：专注于业务逻辑实现
- **DAO 层**：专注于数据持久化

### 2. **可维护性** 🔧
- 各层职责清晰，易于维护
- 业务逻辑集中在 Service 层
- 验证逻辑集中在 Controller 层

### 3. **可测试性** 🧪
- Service 层测试更纯粹，专注业务逻辑
- Controller 层测试关注接入逻辑
- 单元测试更容易编写

### 4. **可复用性** 🔄
- Service 层可以被多种接入方式复用
- 业务逻辑不依赖于特定的接入协议
- 支持 HTTP、gRPC、消息队列等多种接入方式

## 实现示例

### Controller 层示例
```go
// 负责参数验证和接入逻辑
func (c *SagaController) CreateSaga(ctx context.Context, req *CreateSagaTransactionReq) (*CreateSagaTransactionRes, error) {
    // 1. 参数验证
    if err := g.Validator().Data(req).Run(ctx); err != nil {
        return nil, err
    }
    
    // 2. 业务逻辑验证
    if len(req.Steps) == 0 {
        return nil, gerror.New("至少需要一个步骤")
    }
    
    // 3. 调用 Service 层
    return c.sagaService.CreateSagaTransaction(ctx, req)
}
```

### Service 层示例
```go
// 专注业务逻辑实现
func (s *SagaTransactionsService) CreateSagaTransaction(ctx context.Context, req *CreateSagaTransactionReq) (*CreateSagaTransactionRes, error) {
    // 1. 生成业务标识
    sagaId := guid.S()
    
    // 2. 构建业务数据
    sagaTransaction := &do.SagaTransactions{
        SagaId:      sagaId,
        Name:        req.Name,
        Status:      SagaStatusPending,
        CurrentStep: "",
        RetryCount:  0,
        CreatedAt:   gtime.Now(),
        UpdatedAt:   gtime.Now(),
    }
    
    // 3. 执行业务逻辑
    err := dao.SagaTransactions.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
        // 业务处理逻辑
        return s.createSagaSteps(ctx, tx, sagaId, req.Steps)
    })
    
    // 4. 发布业务事件
    s.publishSagaCreatedEvent(ctx, sagaId, req)
    
    return &CreateSagaTransactionRes{
        SagaId: sagaId,
        Status: SagaStatusPending,
    }, nil
}
```

## 验证标签的使用

### 在结构体中定义
```go
// 验证标签供 Controller 层使用
type CreateSagaTransactionReq struct {
    Name       string     `json:"name" v:"required|length:1,100#Saga名称不能为空|Saga名称长度为1-100个字符"`
    Steps      []SagaStep `json:"steps" v:"required|min:1#步骤不能为空|至少需要一个步骤"`
    Context    map[string]interface{} `json:"context"`
    TimeoutSec int        `json:"timeoutSec"`
}
```

### 在 Controller 中使用
```go
// 自动验证请求参数
if err := g.Validator().Data(req).Run(ctx); err != nil {
    return nil, err
}
```

## 错误处理分层

### Controller 层错误处理
- 参数验证错误
- 请求格式错误
- 权限验证错误

### Service 层错误处理
- 业务逻辑错误
- 数据状态错误
- 外部服务调用错误

### DAO 层错误处理
- 数据库连接错误
- SQL 执行错误
- 数据约束错误

## 最佳实践

### 1. **保持层次清晰**
- 不要在 Service 层做参数格式验证
- 不要在 Controller 层写复杂业务逻辑
- 每层只关注自己的职责

### 2. **错误处理一致**
- 统一的错误码和错误消息
- 适当的错误日志记录
- 合理的错误传播机制

### 3. **测试策略**
- Controller 层测试：集成测试，关注接入逻辑
- Service 层测试：单元测试，关注业务逻辑
- DAO 层测试：数据层测试，关注数据操作

### 4. **代码组织**
- 按功能模块组织代码
- 保持接口简洁清晰
- 避免跨层直接调用

## 总结

通过明确的分层架构和职责分离，我们实现了：

- 🎯 **清晰的职责分工**：每层专注于自己的核心功能
- 🔧 **更好的可维护性**：业务逻辑集中，易于修改和扩展
- 🧪 **更强的可测试性**：单元测试更纯粹，集成测试更有针对性
- 🔄 **更高的可复用性**：Service 层可以支持多种接入方式

这种架构设计使得我们的 Saga 分布式事务服务更加健壮和易于维护。 