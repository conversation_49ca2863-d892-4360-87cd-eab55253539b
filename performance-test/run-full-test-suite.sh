#!/bin/bash

# Saga完整性能测试套件
# 功能: 先创建事务，然后使用创建的sagaId进行所有其他接口测试

echo "=== Saga完整性能测试套件 ==="
echo "测试时间: $(date)"
echo "测试流程: 创建 → 补偿 → 查询 → 提交 → 回滚"
echo ""

# 创建结果目录
mkdir -p performance-test/results

# 记录测试开始时的数据量
echo "=== 测试前数据状态 ==="
INITIAL_SAGAS=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_transactions;" 2>/dev/null | tail -1)
INITIAL_STEPS=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)
echo "初始saga数量: $INITIAL_SAGAS"
echo "初始步骤数量: $INITIAL_STEPS"
echo ""

# 第1阶段: 创建Saga事务
echo "=== 阶段1: 创建Saga事务 ==="
echo "配置: 4线程, 20并发, 30秒"
echo "接口: POST /saga/transactions"
echo "目标: 创建大量saga事务并保存sagaId供后续测试使用"
echo ""

wrk -t4 -c20 -d30s --latency -s performance-test/create-saga-test.lua http://localhost:8080/saga/transactions

# 检查创建结果
if [ -f "performance-test/results/created_saga_ids.txt" ]; then
    CREATED_COUNT=$(wc -l < performance-test/results/created_saga_ids.txt)
    echo ""
    echo "✅ 创建阶段完成，记录了 $CREATED_COUNT 个sagaId"
else
    echo "❌ 创建阶段失败，未找到sagaId文件"
    exit 1
fi

echo ""
echo "等待3秒后开始后续测试..."
sleep 3

# 第2阶段: 补偿上报测试
echo "=== 阶段2: 补偿上报测试 ==="
echo "配置: 4线程, 30并发, 30秒"
echo "接口: POST /saga/transactions/compensation"
echo "数据源: 使用阶段1创建的sagaId"
echo ""

BEFORE_COMPENSATION=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)

# 创建临时补偿测试脚本
cat > performance-test/temp-compensation-test.lua << 'EOF'
-- 临时补偿测试脚本 (使用创建的sagaId)
wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

local saga_ids = {}
local file = io.open("performance-test/results/created_saga_ids.txt", "r")
if file then
    for line in file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    file:close()
end

print("加载了 " .. #saga_ids .. " 个新创建的sagaId用于补偿测试")

local counter = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("GET", "/health", nil, nil)
    end
    
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    local action = "FullTestCompensation" .. counter
    local service = "full-test-service-" .. (counter % 50)
    
    local body = string.format([[{
        "sagaId": "%s",
        "action": "%s",
        "serviceName": "%s",
        "contextData": "{\"orderId\":\"ORDER-%d\",\"testType\":\"full_suite\"}",
        "compensationContext": "{\"sagaId\":\"%s\",\"action\":\"%s\",\"reason\":\"full_test\"}",
        "compensateEndpoint": "http://%s:8080/saga/compensate/%s"
    }]], saga_id, action, service, counter, saga_id, action, service, action)

    return wrk.format("POST", nil, nil, body)
end

function done(summary, latency, requests)
    print("\n=== 补偿上报测试结果 ===")
    print(string.format("使用sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
end
EOF

wrk -t4 -c30 -d30s --latency -s performance-test/temp-compensation-test.lua http://localhost:8080/saga/transactions/compensation

AFTER_COMPENSATION=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)
COMPENSATION_ADDED=$((AFTER_COMPENSATION - BEFORE_COMPENSATION))
echo ""
echo "✅ 补偿阶段完成，新增 $COMPENSATION_ADDED 个步骤"

echo ""
echo "等待3秒后开始查询测试..."
sleep 3

# 第3阶段: 状态查询测试
echo "=== 阶段3: 状态查询测试 ==="
echo "配置: 8线程, 50并发, 30秒"
echo "接口: GET /saga/transactions/{sagaId}"
echo "数据源: 使用阶段1创建的sagaId"
echo ""

wrk -t8 -c50 -d30s --latency -s performance-test/query-real-test.lua http://localhost:8080

echo ""
echo "等待3秒后开始提交测试..."
sleep 3

# 第4阶段: 事务提交测试
echo "=== 阶段4: 事务提交测试 ==="
echo "配置: 4线程, 20并发, 20秒"
echo "接口: POST /saga/transactions/commit"
echo "数据源: 使用阶段1创建的sagaId"
echo ""

wrk -t4 -c20 -d20s --latency -s performance-test/commit-real-test.lua http://localhost:8080

echo ""
echo "等待3秒后开始回滚测试..."
sleep 3

# 第5阶段: 事务回滚测试
echo "=== 阶段5: 事务回滚测试 ==="
echo "配置: 4线程, 20并发, 20秒"
echo "接口: POST /saga/transactions/rollback"
echo "数据源: 使用阶段1创建的sagaId"
echo ""

wrk -t4 -c20 -d20s --latency -s performance-test/rollback-real-test.lua http://localhost:8080

# 清理临时文件
rm -f performance-test/temp-compensation-test.lua

# 生成最终报告
echo ""
echo "=== 完整测试套件总结 ==="
FINAL_SAGAS=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_transactions;" 2>/dev/null | tail -1)
FINAL_STEPS=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)

TOTAL_SAGAS_ADDED=$((FINAL_SAGAS - INITIAL_SAGAS))
TOTAL_STEPS_ADDED=$((FINAL_STEPS - INITIAL_STEPS))

echo "✅ 创建saga事务: $TOTAL_SAGAS_ADDED 个"
echo "✅ 新增补偿步骤: $TOTAL_STEPS_ADDED 个"
echo "✅ 测试覆盖: 创建 → 补偿 → 查询 → 提交 → 回滚"
echo "✅ 数据链路: 所有测试都使用真实创建的sagaId"
echo "✅ 业务约束: 严格遵循API规范和业务逻辑"
echo ""
echo "测试完成时间: $(date)"
echo "结果文件: performance-test/results/created_saga_ids.txt"
echo ""
echo "🎉 Saga完整性能测试套件执行完成！"
