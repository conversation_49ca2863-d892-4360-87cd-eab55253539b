-- Saga事务创建性能测试脚本 (保存sagaId版本)
wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 请求计数器
local counter = 0
local created_saga_ids = {}
local saga_file = nil

-- 初始化函数
function init(args)
    counter = 0
    -- 创建结果目录
    os.execute("mkdir -p performance-test/results")
    -- 打开文件用于保存创建的sagaId
    saga_file = io.open("performance-test/results/created_saga_ids.txt", "w")
    if saga_file then
        print("开始记录创建的sagaId到文件...")
    end
end

-- 生成请求
function request()
    counter = counter + 1
    local name = "性能测试事务-" .. counter .. "-" .. math.random(100000)
    local body = '{"name": "' .. name .. '", "stepIndexMode": "auto", "compensationWindowSec": 300}'
    return wrk.format("POST", nil, nil, body)
end

-- 响应处理
function response(status, headers, body)
    if status == 200 then
        -- 解析响应获取sagaId
        local saga_id = body:match('"sagaId":"([^"]+)"')
        if saga_id and saga_file then
            saga_file:write(saga_id .. "\n")
            saga_file:flush() -- 立即写入文件
            table.insert(created_saga_ids, saga_id)
        end
    else
        print("Error: " .. status .. " - " .. body)
    end
end

-- 测试完成处理
function done(summary, latency, requests)
    if saga_file then
        saga_file:close()
    end

    print("\n=== Saga事务创建测试结果 ===")
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", summary.requests - summary.errors.connect - summary.errors.read - summary.errors.write - summary.errors.status - summary.errors.timeout))
    print(string.format("错误数: %d", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
    print(string.format("创建的sagaId数量: %d", #created_saga_ids))
    print("sagaId已保存到: performance-test/results/created_saga_ids.txt")
end
