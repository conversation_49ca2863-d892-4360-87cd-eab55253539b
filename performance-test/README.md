# Saga 性能测试脚本

本目录包含 Saga 分布式事务系统的性能测试脚本，所有脚本都基于真实创建的sagaId，确保测试结果的准确性和可信度。

## � 核心脚本

### 基础测试脚本
- `create-saga-test.lua`: 创建事务并保存sagaId ⭐
- `compensation-unique-test.lua`: 补偿上报测试 (使用唯一组合)
- `query-real-test.lua`: 状态查询测试
- `commit-real-test.lua`: 事务提交测试
- `rollback-real-test.lua`: 事务回滚测试

### 执行脚本
- `run-combined-test.sh`: 组合测试 (创建+补偿)
- `run-full-test-suite.sh`: 完整测试套件 ⭐

### 数据脚本
- `initialize-test-data.sql`: 初始化测试数据脚本

## 🚀 使用方法

### 方式1: 完整测试套件 (推荐)
```bash
# 执行完整测试套件 (创建 → 补偿 → 查询 → 提交 → 回滚)
chmod +x run-full-test-suite.sh
./run-full-test-suite.sh
```

### 方式2: 组合测试
```bash
# 执行组合测试 (创建 + 补偿)
chmod +x run-combined-test.sh
./run-combined-test.sh
```

### 方式3: 单独测试
```bash
# 1. 创建事务 (必须先执行，生成sagaId)
wrk -t4 -c20 -d30s --latency -s create-saga-test.lua http://localhost:8080/saga/transactions

# 2. 补偿上报 (使用创建的sagaId)
wrk -t4 -c30 -d30s --latency -s compensation-unique-test.lua http://localhost:8080/saga/transactions/compensation

# 3. 状态查询 (使用创建的sagaId)
wrk -t8 -c50 -d30s --latency -s query-real-test.lua http://localhost:8080

# 4. 事务提交 (使用创建的sagaId)
wrk -t4 -c20 -d20s --latency -s commit-real-test.lua http://localhost:8080

# 5. 事务回滚 (使用创建的sagaId)
wrk -t4 -c20 -d20s --latency -s rollback-real-test.lua http://localhost:8080
```

## 📊 测试数据准备

### 初始化大量测试数据
```bash
# 执行数据初始化脚本 (生成10万个saga + 35万个步骤)
docker exec -i saga-mysql mysql -u root -p12345678a saga < initialize-test-data.sql
```

## 🎯 数据链路说明

所有测试脚本都遵循统一的数据链路：

```
create-saga-test.lua → 生成真实sagaId → 保存到文件
                                           ↓
其他测试脚本 ← 读取真实sagaId ← results/created_saga_ids.txt
```

### 数据文件
- `results/created_saga_ids.txt`: 创建测试生成的sagaId列表
- `results/combined_created_saga_ids.txt`: 组合测试生成的sagaId列表

## ✅ 业务约束

- ✅ **真实数据**: 所有测试都使用真实创建的sagaId
- ✅ **API规范**: 严格遵循接口文档规范
- ✅ **唯一性**: 补偿测试确保action+service组合唯一
- ✅ **状态约束**: 新创建的saga适合各种操作测试

## ⚠️ 注意事项

1. **执行顺序**: 必须先运行create-saga-test.lua生成sagaId
2. **服务状态**: 确保Saga服务正在运行 (http://localhost:8080)
3. **数据库连接**: 确保数据库连接正常
4. **资源监控**: 监控系统资源使用情况
5. **并发调整**: 根据实际情况调整并发数和测试时间

## 📈 性能基准

基于最新测试结果的性能基准：

| 接口 | QPS | 延迟 | P99延迟 | 评价 |
|------|-----|------|---------|------|
| 创建事务 | 6,261.94 | 5.02ms | 32.57ms | ✅ 优秀 |
| 补偿上报 | 2,739.44 | 10.52ms | 31.62ms | ✅ 良好 |
| 状态查询 | 10,311.80 | 2.07ms | 6.85ms | ✅ 优秀 |
| 事务提交 | 4,708.88 | 2.75ms | 9.41ms | ✅ 优秀 |
| 事务回滚 | 17,774.54 | 0.72ms | 2.23ms | ✅ 卓越 |
