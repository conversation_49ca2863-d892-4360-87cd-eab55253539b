#!/bin/bash

# Saga组合性能测试执行脚本
# 功能: 先创建事务，然后使用创建的sagaId进行补偿上报测试

echo "=== Saga组合性能测试 ==="
echo "测试时间: $(date)"
echo ""

# 创建结果目录
mkdir -p performance-test/results

# 第1阶段: 创建Saga事务
echo "=== 阶段1: 创建Saga事务 ==="
echo "配置: 4线程, 20并发, 30秒"
echo "接口: POST /saga/transactions"
echo "目标: 创建大量saga事务并保存sagaId"
echo ""

# 记录创建前的数据量
BEFORE_SAGAS=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_transactions;" 2>/dev/null | tail -1)
echo "创建前saga数量: $BEFORE_SAGAS"

# 执行创建测试
wrk -t4 -c20 -d30s --latency -s performance-test/create-saga-test.lua http://localhost:8080/saga/transactions

# 记录创建后的数据量
AFTER_SAGAS=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_transactions;" 2>/dev/null | tail -1)
CREATED_COUNT=$((AFTER_SAGAS - BEFORE_SAGAS))
echo ""
echo "创建后saga数量: $AFTER_SAGAS"
echo "本次创建数量: $CREATED_COUNT"

# 检查创建的sagaId文件
if [ -f "performance-test/results/created_saga_ids.txt" ]; then
    FILE_COUNT=$(wc -l < performance-test/results/created_saga_ids.txt)
    echo "文件中记录的sagaId数量: $FILE_COUNT"
else
    echo "❌ 未找到创建的sagaId文件"
    exit 1
fi

echo ""
echo "等待5秒后开始补偿上报测试..."
sleep 5

# 第2阶段: 补偿上报测试
echo "=== 阶段2: 补偿上报测试 (使用创建的sagaId) ==="
echo "配置: 4线程, 30并发, 30秒"
echo "接口: POST /saga/transactions/compensation"
echo "数据源: 使用阶段1创建的sagaId"
echo ""

# 创建使用创建sagaId的补偿测试脚本
cat > performance-test/compensation-from-created.lua << 'EOF'
-- 补偿上报测试脚本 (使用创建的sagaId)
wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 读取创建的sagaId
local saga_ids = {}
local file = io.open("performance-test/results/created_saga_ids.txt", "r")
if file then
    for line in file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    file:close()
end

print("加载了 " .. #saga_ids .. " 个新创建的sagaId用于补偿测试")

local counter = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("GET", "/health", nil, nil)
    end
    
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    
    -- 生成唯一的action和service组合
    local action = "CreatedSagaAction" .. counter
    local service = "created-saga-service-" .. (counter % 100)
    
    local body = string.format([[{
        "sagaId": "%s",
        "action": "%s",
        "serviceName": "%s",
        "contextData": "{\"orderId\":\"ORDER-%d\",\"amount\":%.2f,\"source\":\"created_saga\"}",
        "compensationContext": "{\"sagaId\":\"%s\",\"action\":\"%s\",\"reason\":\"created_saga_test\"}",
        "compensateEndpoint": "http://%s:8080/saga/compensate/%s"
    }]], 
        saga_id, 
        action, 
        service,
        counter,
        99.99 + (counter % 900),
        saga_id,
        action,
        service,
        action
    )

    return wrk.format("POST", nil, nil, body)
end

function done(summary, latency, requests)
    print("\n=== 补偿上报测试结果 (使用创建的sagaId) ===")
    print(string.format("使用创建的sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", summary.requests - summary.errors.connect - summary.errors.read - summary.errors.write - summary.errors.status - summary.errors.timeout))
    print(string.format("错误数: %d", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
end
EOF

# 记录补偿前的步骤数量
BEFORE_STEPS=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)
echo "补偿前步骤数量: $BEFORE_STEPS"

# 执行补偿测试
wrk -t4 -c30 -d30s --latency -s performance-test/compensation-from-created.lua http://localhost:8080/saga/transactions/compensation

# 记录补偿后的步骤数量
AFTER_STEPS=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)
COMPENSATION_COUNT=$((AFTER_STEPS - BEFORE_STEPS))
echo ""
echo "补偿后步骤数量: $AFTER_STEPS"
echo "本次补偿上报数量: $COMPENSATION_COUNT"

# 生成测试报告
echo ""
echo "=== 组合测试总结 ==="
echo "✅ 创建saga事务: $CREATED_COUNT 个"
echo "✅ 补偿上报步骤: $COMPENSATION_COUNT 个"
echo "✅ 业务约束: 使用真实创建的sagaId"
echo "✅ 数据完整性: 所有操作都基于真实数据"
echo ""
echo "测试完成时间: $(date)"
echo "结果文件: performance-test/results/created_saga_ids.txt"
