-- 补偿上报性能测试脚本 (确保唯一组合)
-- 业务约束: 使用真实sagaId，确保action+service组合唯一

wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 读取真实的sagaId池
local saga_ids = {}
local file = io.open("performance-test/results/created_saga_ids.txt", "r")
if file then
    for line in file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    file:close()
end

print("加载了 " .. #saga_ids .. " 个真实sagaId用于测试")

local counter = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("GET", "/health", nil, nil)
    end
    
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    
    -- 生成唯一的action和service组合 (使用counter确保唯一性)
    local action = "PerfTestAction" .. counter
    local service = "perf-test-service-" .. (counter % 100)
    
    -- 生成符合业务逻辑的补偿上报数据
    local body = string.format([[{
        "sagaId": "%s",
        "action": "%s",
        "serviceName": "%s",
        "contextData": "{\"orderId\":\"ORDER-%d\",\"amount\":%.2f,\"timestamp\":%d}",
        "compensationContext": "{\"sagaId\":\"%s\",\"action\":\"%s\",\"reason\":\"performance_test\"}",
        "compensateEndpoint": "http://%s:8080/saga/compensate/%s"
    }]], 
        saga_id, 
        action, 
        service,
        counter,
        99.99 + (counter % 900),
        os.time(),
        saga_id,
        action,
        service,
        action
    )

    return wrk.format("POST", nil, nil, body)
end

function done(summary, latency, requests)
    print("\n=== 补偿上报接口性能测试结果 (唯一组合) ===")
    print(string.format("使用真实sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", summary.requests - summary.errors.connect - summary.errors.read - summary.errors.write - summary.errors.status - summary.errors.timeout))
    print(string.format("错误数: %d", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
    print("注意: 使用唯一的action+service组合，应该能看到数据库中步骤数据增加")
end
