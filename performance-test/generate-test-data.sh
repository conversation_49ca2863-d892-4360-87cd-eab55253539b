#!/bin/bash

# Saga 分布式事务系统 - 动态数据初始化脚本
# 支持通过环境变量动态设置数据量级

set -e

# 默认配置
DEFAULT_TOTAL_SAGAS=100000
DEFAULT_BATCH_SIZE=10000
DEFAULT_MIN_STEPS=3
DEFAULT_MAX_STEPS=5

# 从环境变量读取配置，如果未设置则使用默认值
TOTAL_SAGAS=${SAGA_TOTAL_COUNT:-$DEFAULT_TOTAL_SAGAS}
BATCH_SIZE=${SAGA_BATCH_SIZE:-$DEFAULT_BATCH_SIZE}
MIN_STEPS=${SAGA_MIN_STEPS:-$DEFAULT_MIN_STEPS}
MAX_STEPS=${SAGA_MAX_STEPS:-$DEFAULT_MAX_STEPS}

# 数据量级预设
case "${SAGA_DATA_LEVEL:-}" in
    "10W"|"100K")
        TOTAL_SAGAS=100000
        BATCH_SIZE=10000
        echo "📊 使用预设: 10万级数据量"
        ;;
    "100W"|"1M")
        TOTAL_SAGAS=1000000
        BATCH_SIZE=50000
        echo "📊 使用预设: 100万级数据量"
        ;;
    "1000W"|"10M")
        TOTAL_SAGAS=10000000
        BATCH_SIZE=100000
        echo "📊 使用预设: 1000万级数据量"
        ;;
    "")
        echo "📊 使用自定义配置或默认值"
        ;;
    *)
        echo "❌ 无效的数据量级: ${SAGA_DATA_LEVEL}"
        echo "支持的量级: 10W, 100W, 1000W"
        exit 1
        ;;
esac

# 输出目录
OUTPUT_DIR="performance-test/results"
mkdir -p "$OUTPUT_DIR"

# 生成的SQL文件名
OUTPUT_FILE="$OUTPUT_DIR/initialize-test-data-${TOTAL_SAGAS}.sql"

echo "🚀 开始生成动态测试数据初始化脚本..."
echo "📋 配置信息:"
echo "   - 总Saga数量: $(printf "%'d" $TOTAL_SAGAS)"
echo "   - 批处理大小: $(printf "%'d" $BATCH_SIZE)"
echo "   - 每个Saga步骤数: $MIN_STEPS-$MAX_STEPS"
echo "   - 预计总步骤数: $(printf "%'d" $((TOTAL_SAGAS * (MIN_STEPS + MAX_STEPS) / 2)))"
echo "   - 输出文件: $OUTPUT_FILE"

# 生成SQL文件
cat > "$OUTPUT_FILE" << EOF
-- Saga 分布式事务系统 - 动态初始化测试数据脚本
-- 生成时间: $(date '+%Y年%m月%d日 %H:%M:%S')
-- 数据量级: $(printf "%'d" $TOTAL_SAGAS) 个Saga事务
-- 生成方式: 环境变量动态配置

USE saga;

-- 清理现有测试数据
TRUNCATE TABLE saga_steps;
TRUNCATE TABLE saga_transactions;

-- 重置自增ID
ALTER TABLE saga_transactions AUTO_INCREMENT = 1;
ALTER TABLE saga_steps AUTO_INCREMENT = 1;

-- 动态设置变量
SET @batch_size = $BATCH_SIZE;  -- 每批处理数量
SET @total_sagas = $TOTAL_SAGAS; -- 总saga数量
SET @min_steps_per_saga = $MIN_STEPS; -- 每个saga最少步骤数
SET @max_steps_per_saga = $MAX_STEPS; -- 每个saga最多步骤数

-- 显示配置信息
SELECT 
    '=== 动态数据初始化配置 ===' as info,
    CONCAT('总Saga数量: ', FORMAT(@total_sagas, 0)) as saga_count,
    CONCAT('批处理大小: ', FORMAT(@batch_size, 0)) as batch_size,
    CONCAT('步骤数范围: ', @min_steps_per_saga, '-', @max_steps_per_saga) as steps_range,
    CONCAT('预计总步骤: ', FORMAT(@total_sagas * (@min_steps_per_saga + @max_steps_per_saga) / 2, 0)) as estimated_steps;

-- 创建临时表用于生成数据
CREATE TEMPORARY TABLE temp_saga_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    saga_id CHAR(36) NOT NULL,
    name VARCHAR(64) NOT NULL,
    saga_status ENUM('pending', 'running', 'completed', 'compensating', 'failed') NOT NULL,
    step_index_mode ENUM('manual', 'auto') NOT NULL,
    steps_count INT NOT NULL,
    business_type VARCHAR(20) NOT NULL
);

-- 生成Saga事务数据
INSERT INTO temp_saga_data (saga_id, name, saga_status, step_index_mode, steps_count, business_type)
SELECT 
    -- 生成UUID格式的saga_id (36位标准UUID格式)
    CONCAT(
        SUBSTRING(MD5(CONCAT('saga', n, UNIX_TIMESTAMP())), 1, 8), '-',
        SUBSTRING(MD5(CONCAT('test', n, RAND())), 1, 4), '-',
        '4000', '-',
        SUBSTRING(MD5(CONCAT('data', n, NOW())), 1, 4), '-',
        SUBSTRING(MD5(CONCAT('init', n, CONNECTION_ID())), 1, 12)
    ) as saga_id,
    
    -- 生成业务相关的名称
    CASE (n % 5)
        WHEN 0 THEN CONCAT('电商订单流程-', LPAD(n, 8, '0'))
        WHEN 1 THEN CONCAT('支付处理流程-', LPAD(n, 8, '0'))
        WHEN 2 THEN CONCAT('库存管理流程-', LPAD(n, 8, '0'))
        WHEN 3 THEN CONCAT('用户注册流程-', LPAD(n, 8, '0'))
        ELSE CONCAT('数据同步流程-', LPAD(n, 8, '0'))
    END as name,
    
    -- 状态分布: running(60%), completed(20%), pending(10%), compensating(5%), failed(5%)
    CASE 
        WHEN (n % 100) < 60 THEN 'running'
        WHEN (n % 100) < 80 THEN 'completed'
        WHEN (n % 100) < 90 THEN 'pending'
        WHEN (n % 100) < 95 THEN 'compensating'
        ELSE 'failed'
    END as saga_status,
    
    -- StepIndexMode: auto(80%), manual(20%)
    CASE WHEN (n % 5) = 0 THEN 'manual' ELSE 'auto' END as step_index_mode,
    
    -- 每个saga的步骤数量 (3-5个步骤)
    CASE 
        WHEN (n % 10) < 6 THEN @min_steps_per_saga      -- 60%: 3个步骤
        WHEN (n % 10) < 9 THEN @min_steps_per_saga + 1  -- 30%: 4个步骤
        ELSE @max_steps_per_saga                         -- 10%: 5个步骤
    END as steps_count,
    
    -- 业务类型分布
    CASE (n % 5)
        WHEN 0 THEN 'ecommerce'
        WHEN 1 THEN 'payment'
        WHEN 2 THEN 'inventory'
        WHEN 3 THEN 'user'
        ELSE 'sync'
    END as business_type

FROM (
    SELECT a.N + b.N * 10 + c.N * 100 + d.N * 1000 + e.N * 10000 + f.N * 100000 + g.N * 1000000 + h.N * 10000000 as n
    FROM 
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) e,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) f,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) g,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) h
) numbers
WHERE numbers.n < @total_sagas
ORDER BY numbers.n;

-- 显示进度
SELECT CONCAT('已创建 ', COUNT(*), ' 个Saga事务') as progress FROM temp_saga_data;

-- 批量插入Saga事务数据
INSERT INTO saga_transactions (saga_id, name, saga_status, step_index_mode, created_at, updated_at)
SELECT saga_id, name, saga_status, step_index_mode, NOW(), NOW()
FROM temp_saga_data;

-- 优化表统计信息
ANALYZE TABLE saga_transactions;
ANALYZE TABLE saga_steps;

-- 显示最终统计
SELECT '=== 动态数据初始化完成 ===' as status;
SELECT 
    'saga_transactions' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM saga_steps;

SELECT CONCAT('数据比例 - 步骤:事务 = ', 
    (SELECT COUNT(*) FROM saga_steps), ':', 
    (SELECT COUNT(*) FROM saga_transactions), ' ≈ ',
    ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 2), ':1'
) as data_ratio;

SELECT CONCAT('✅ 动态数据初始化完成！数据量级: ', FORMAT(@total_sagas, 0), ' 个Saga事务') as final_status;

EOF

echo "✅ SQL文件生成完成: $OUTPUT_FILE"
echo ""
echo "🔧 使用方法:"
echo "   docker exec -i saga-mysql mysql -u root -p12345678a saga < $OUTPUT_FILE"
echo ""
echo "📋 环境变量配置示例:"
echo "   # 10万级数据"
echo "   export SAGA_DATA_LEVEL=10W"
echo "   ./performance-test/generate-test-data.sh"
echo ""
echo "   # 100万级数据"
echo "   export SAGA_DATA_LEVEL=100W"
echo "   ./performance-test/generate-test-data.sh"
echo ""
echo "   # 1000万级数据"
echo "   export SAGA_DATA_LEVEL=1000W"
echo "   ./performance-test/generate-test-data.sh"
echo ""
echo "   # 自定义数量"
echo "   export SAGA_TOTAL_COUNT=500000"
echo "   export SAGA_BATCH_SIZE=25000"
echo "   ./performance-test/generate-test-data.sh"
