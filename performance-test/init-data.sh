#!/bin/bash

# Saga 分布式事务系统 - 一键数据初始化脚本
# 集成生成和执行功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "Saga 分布式事务系统 - 动态数据初始化工具"
    echo ""
    echo "用法:"
    echo "  $0 [数据量级]"
    echo ""
    echo "支持的数据量级:"
    echo "  10W    - 10万级数据 (100,000个Saga事务)"
    echo "  100W   - 100万级数据 (1,000,000个Saga事务)"
    echo "  1000W  - 1000万级数据 (10,000,000个Saga事务)"
    echo "  custom - 自定义数量 (通过环境变量配置)"
    echo ""
    echo "环境变量配置 (用于custom模式):"
    echo "  SAGA_TOTAL_COUNT  - 总Saga数量 (默认: 100000)"
    echo "  SAGA_BATCH_SIZE   - 批处理大小 (默认: 10000)"
    echo "  SAGA_MIN_STEPS    - 最少步骤数 (默认: 3)"
    echo "  SAGA_MAX_STEPS    - 最多步骤数 (默认: 5)"
    echo ""
    echo "示例:"
    echo "  $0 10W                    # 初始化10万级数据"
    echo "  $0 100W                   # 初始化100万级数据"
    echo "  $0 1000W                  # 初始化1000万级数据"
    echo "  SAGA_TOTAL_COUNT=500000 $0 custom  # 自定义50万数据"
    echo ""
    echo "注意:"
    echo "  - 执行前会清空现有数据"
    echo "  - 大数据量初始化可能需要较长时间"
    echo "  - 确保Docker容器正在运行"
}

# 检查Docker容器状态
check_docker() {
    print_info "检查Docker容器状态..."
    
    if ! docker ps | grep -q "saga-mysql"; then
        print_error "MySQL容器未运行，请先启动服务"
        echo "执行: make up 或 make up-perf"
        exit 1
    fi
    
    if ! docker ps | grep -q "saga-app"; then
        print_warning "应用容器未运行，但可以继续数据初始化"
    fi
    
    print_success "Docker容器检查完成"
}

# 估算执行时间和资源需求
estimate_resources() {
    local total_sagas=$1
    local estimated_steps=$((total_sagas * 4))  # 平均4个步骤
    local estimated_time_min
    local estimated_size_mb
    
    # 根据数据量估算时间 (基于经验值)
    if [ $total_sagas -le 100000 ]; then
        estimated_time_min=2
        estimated_size_mb=50
    elif [ $total_sagas -le 1000000 ]; then
        estimated_time_min=15
        estimated_size_mb=500
    else
        estimated_time_min=120
        estimated_size_mb=5000
    fi
    
    echo ""
    print_info "资源需求估算:"
    echo "  📊 Saga事务数: $(printf "%'d" $total_sagas)"
    echo "  📊 预计步骤数: $(printf "%'d" $estimated_steps)"
    echo "  ⏱️  预计耗时: ~${estimated_time_min}分钟"
    echo "  💾 预计存储: ~${estimated_size_mb}MB"
    echo ""
}

# 确认执行
confirm_execution() {
    local data_level=$1
    
    echo ""
    print_warning "即将清空现有数据并初始化 ${data_level} 级别的测试数据"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "操作已取消"
        exit 0
    fi
}

# 执行数据初始化
execute_initialization() {
    local data_level=$1
    
    print_info "开始数据初始化..."
    
    # 设置环境变量
    export SAGA_DATA_LEVEL=$data_level
    
    # 生成SQL文件
    print_info "生成动态SQL脚本..."
    if ! ./performance-test/generate-test-data.sh; then
        print_error "SQL脚本生成失败"
        exit 1
    fi
    
    # 确定SQL文件路径
    local total_sagas
    case "$data_level" in
        "10W") total_sagas=100000 ;;
        "100W") total_sagas=1000000 ;;
        "1000W") total_sagas=10000000 ;;
        "custom") total_sagas=${SAGA_TOTAL_COUNT:-100000} ;;
    esac
    
    local sql_file="performance-test/results/initialize-test-data-${total_sagas}.sql"
    
    if [ ! -f "$sql_file" ]; then
        print_error "SQL文件未找到: $sql_file"
        exit 1
    fi
    
    # 执行SQL脚本
    print_info "执行数据初始化脚本..."
    echo "📁 SQL文件: $sql_file"
    
    local start_time=$(date +%s)
    
    if docker exec -i saga-mysql mysql -u root -p12345678a saga < "$sql_file"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        print_success "数据初始化完成！"
        echo "⏱️  实际耗时: ${duration}秒"
        
        # 验证数据
        print_info "验证初始化结果..."
        docker exec saga-mysql mysql -u root -p12345678a -e "
        USE saga;
        SELECT 
            CONCAT('✅ Saga事务: ', FORMAT(COUNT(*), 0), ' 个') as saga_result
        FROM saga_transactions;
        SELECT 
            CONCAT('✅ 步骤数据: ', FORMAT(COUNT(*), 0), ' 个') as step_result
        FROM saga_steps;
        SELECT 
            CONCAT('✅ 数据比例: ', 
                ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 2),
                ':1') as ratio_result;
        "
        
    else
        print_error "数据初始化失败"
        exit 1
    fi
}

# 主函数
main() {
    echo "🚀 Saga 分布式事务系统 - 动态数据初始化工具"
    echo "=================================================="
    
    # 检查参数
    if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    local data_level=$1
    
    # 验证数据量级参数
    case "$data_level" in
        "10W"|"100W"|"1000W"|"custom")
            ;;
        *)
            print_error "无效的数据量级: $data_level"
            echo ""
            show_help
            exit 1
            ;;
    esac
    
    # 检查脚本权限
    if [ ! -x "./performance-test/generate-test-data.sh" ]; then
        print_info "设置脚本执行权限..."
        chmod +x ./performance-test/generate-test-data.sh
    fi
    
    # 检查Docker环境
    check_docker
    
    # 估算资源需求
    local total_sagas
    case "$data_level" in
        "10W") total_sagas=100000 ;;
        "100W") total_sagas=1000000 ;;
        "1000W") total_sagas=10000000 ;;
        "custom") total_sagas=${SAGA_TOTAL_COUNT:-100000} ;;
    esac
    
    estimate_resources $total_sagas
    
    # 确认执行
    confirm_execution $data_level
    
    # 执行初始化
    execute_initialization $data_level
    
    echo ""
    print_success "🎉 数据初始化流程完成！"
    echo ""
    print_info "接下来可以:"
    echo "  1. 运行性能测试: wrk -t4 -c20 -d30s --latency -s performance-test/create-saga-test.lua http://localhost:8080/saga/transactions"
    echo "  2. 查看数据统计: docker exec saga-mysql mysql -u root -p12345678a -e 'USE saga; SELECT COUNT(*) FROM saga_transactions;'"
    echo "  3. 执行完整测试套件: ./performance-test/run-full-test-suite.sh"
}

# 执行主函数
main "$@"
