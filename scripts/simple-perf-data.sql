-- =====================================================
-- 简化版性能测试数据生成脚本
-- 专门用于大规模数据生成，避免字段长度问题
-- =====================================================

USE saga;

-- 简化版性能测试数据生成
DROP PROCEDURE IF EXISTS GenerateSimplePerfData;
DELIMITER $$
CREATE PROCEDURE GenerateSimplePerfData(IN saga_count INT)
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE saga_id VARCHAR(36);
    DECLARE step_count INT;
    DECLARE j INT;
    DECLARE saga_status_val VARCHAR(20);
    DECLARE compensation_status_val VARCHAR(20);
    DECLARE step_index_mode_val VARCHAR(10);
    
    -- 开始事务
    START TRANSACTION;
    
    SELECT CONCAT('开始生成 ', saga_count, ' 条 Saga 事务数据...') AS message;
    
    WHILE i <= saga_count DO
        -- 生成简化的 saga_id
        SET saga_id = CONCAT('perf-saga-', LPAD(i, 10, '0'));
        SET step_count = FLOOR(2 + RAND() * 4); -- 2-5个步骤
        
        -- 随机选择 Saga 状态
        SET saga_status_val = CASE FLOOR(RAND() * 10)
            WHEN 0 THEN 'pending'
            WHEN 1 THEN 'pending'
            WHEN 2 THEN 'running'
            WHEN 3 THEN 'running'
            WHEN 4 THEN 'running'
            WHEN 5 THEN 'completed'
            WHEN 6 THEN 'completed'
            WHEN 7 THEN 'compensating'
            WHEN 8 THEN 'failed'
            ELSE 'completed'
        END;
        
        -- 随机选择步骤索引模式
        SET step_index_mode_val = IF(RAND() < 0.7, 'auto', 'manual');
        
        -- 插入 Saga 事务
        INSERT INTO saga_transactions (
            saga_id, name, saga_status, step_index_mode,
            cur_step_index, compensation_window_sec, created_at, updated_at
        ) VALUES (
            saga_id,
            CONCAT('性能测试事务_', LPAD(i, 8, '0')),
            saga_status_val,
            step_index_mode_val,
            step_count,
            300,
            DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY),
            NOW()
        );
        
        -- 插入步骤数据
        SET j = 1;
        WHILE j <= step_count DO
            -- 根据 Saga 状态决定补偿状态
            SET compensation_status_val = CASE 
                WHEN saga_status_val = 'pending' THEN 'uninitialized'
                WHEN saga_status_val = 'running' THEN 
                    CASE FLOOR(RAND() * 3)
                        WHEN 0 THEN 'uninitialized'
                        WHEN 1 THEN 'pending'
                        ELSE 'completed'
                    END
                WHEN saga_status_val = 'completed' THEN 'completed'
                WHEN saga_status_val = 'compensating' THEN
                    CASE FLOOR(RAND() * 5)
                        WHEN 0 THEN 'pending'
                        WHEN 1 THEN 'running'
                        WHEN 2 THEN 'completed'
                        WHEN 3 THEN 'failed'
                        ELSE 'delay'
                    END
                WHEN saga_status_val = 'failed' THEN
                    CASE FLOOR(RAND() * 3)
                        WHEN 0 THEN 'completed'
                        WHEN 1 THEN 'failed'
                        ELSE 'pending'
                    END
                ELSE 'uninitialized'
            END;
            
            INSERT INTO saga_steps (
                step_id, saga_id, action, step_index, service_name,
                context_data, compensation_context, compensate_endpoint,
                compensation_status, last_error, retry_count,
                created_at, updated_at
            ) VALUES (
                CONCAT('s', LPAD(i, 5, '0'), j),
                saga_id,
                CONCAT('Action', j),
                j,
                CONCAT('service-', j),
                '{"test": "data"}',
                '{"compensate": "data"}',
                CONCAT('http://service-', j, '/compensate'),
                compensation_status_val,
                IF(compensation_status_val = 'failed', '补偿执行失败', NULL),
                IF(compensation_status_val IN ('failed', 'running'), FLOOR(RAND() * 3), 0),
                DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY),
                NOW()
            );
            SET j = j + 1;
        END WHILE;
        
        SET i = i + 1;
        
        -- 每1000条提交一次
        IF i % 1000 = 0 THEN
            COMMIT;
            START TRANSACTION;
            SELECT CONCAT('已生成 ', i, ' 条 Saga 事务数据...') AS progress;
        END IF;
    END WHILE;
    
    COMMIT;
    
    SELECT '✅ 性能测试数据生成完成！' AS message;
    
    -- 输出统计信息
    SELECT 
        COUNT(*) as total_sagas,
        SUM(CASE WHEN saga_status = 'pending' THEN 1 ELSE 0 END) as pending_sagas,
        SUM(CASE WHEN saga_status = 'running' THEN 1 ELSE 0 END) as running_sagas,
        SUM(CASE WHEN saga_status = 'completed' THEN 1 ELSE 0 END) as completed_sagas,
        SUM(CASE WHEN saga_status = 'compensating' THEN 1 ELSE 0 END) as compensating_sagas,
        SUM(CASE WHEN saga_status = 'failed' THEN 1 ELSE 0 END) as failed_sagas
    FROM saga_transactions;
    
    SELECT 
        COUNT(*) as total_steps,
        SUM(CASE WHEN compensation_status = 'uninitialized' THEN 1 ELSE 0 END) as uninitialized_steps,
        SUM(CASE WHEN compensation_status = 'pending' THEN 1 ELSE 0 END) as pending_steps,
        SUM(CASE WHEN compensation_status = 'running' THEN 1 ELSE 0 END) as running_steps,
        SUM(CASE WHEN compensation_status = 'completed' THEN 1 ELSE 0 END) as completed_steps,
        SUM(CASE WHEN compensation_status = 'failed' THEN 1 ELSE 0 END) as failed_steps,
        SUM(CASE WHEN compensation_status = 'delay' THEN 1 ELSE 0 END) as delay_steps
    FROM saga_steps;
    
END$$
DELIMITER ;

-- Level 1: 10万级数据生成
DROP PROCEDURE IF EXISTS GenerateLevel1DataSimple;
DELIMITER $$
CREATE PROCEDURE GenerateLevel1DataSimple()
BEGIN
    SELECT '开始生成 Level 1 数据（10万 Saga 事务）...' AS message;
    CALL GenerateSimplePerfData(100000);
    SELECT '✅ Level 1 数据生成完成！' AS message;
END$$
DELIMITER ;

-- Level 2: 100万级数据生成
DROP PROCEDURE IF EXISTS GenerateLevel2DataSimple;
DELIMITER $$
CREATE PROCEDURE GenerateLevel2DataSimple()
BEGIN
    SELECT '开始生成 Level 2 数据（100万 Saga 事务）...' AS message;
    CALL GenerateSimplePerfData(1000000);
    SELECT '✅ Level 2 数据生成完成！' AS message;
END$$
DELIMITER ;

-- Level 3: 1000万级数据生成
DROP PROCEDURE IF EXISTS GenerateLevel3DataSimple;
DELIMITER $$
CREATE PROCEDURE GenerateLevel3DataSimple()
BEGIN
    SELECT '开始生成 Level 3 数据（1000万 Saga 事务）...' AS message;
    CALL GenerateSimplePerfData(10000000);
    SELECT '✅ Level 3 数据生成完成！' AS message;
END$$
DELIMITER ;

-- =====================================================
-- 数据清理脚本
-- =====================================================

DROP PROCEDURE IF EXISTS CleanTestData;
DELIMITER $$
CREATE PROCEDURE CleanTestData()
BEGIN
    SELECT '开始清理测试数据...' AS message;

    -- 禁用外键检查
    SET FOREIGN_KEY_CHECKS = 0;

    -- 清理数据
    TRUNCATE TABLE saga_steps;
    TRUNCATE TABLE saga_transactions;

    -- 启用外键检查
    SET FOREIGN_KEY_CHECKS = 1;

    SELECT '✅ 测试数据清理完成！' AS message;
END$$
DELIMITER ;

-- =====================================================
-- 数据统计脚本
-- =====================================================

DROP PROCEDURE IF EXISTS ShowDataStatistics;
DELIMITER $$
CREATE PROCEDURE ShowDataStatistics()
BEGIN
    SELECT '📊 数据统计报告' AS title;

    -- Saga 事务统计
    SELECT
        '=== Saga 事务统计 ===' AS section,
        COUNT(*) as total_sagas,
        SUM(CASE WHEN saga_status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN saga_status = 'running' THEN 1 ELSE 0 END) as running,
        SUM(CASE WHEN saga_status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN saga_status = 'compensating' THEN 1 ELSE 0 END) as compensating,
        SUM(CASE WHEN saga_status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN step_index_mode = 'auto' THEN 1 ELSE 0 END) as auto_mode,
        SUM(CASE WHEN step_index_mode = 'manual' THEN 1 ELSE 0 END) as manual_mode
    FROM saga_transactions;

    -- 步骤统计
    SELECT
        '=== 步骤统计 ===' AS section,
        COUNT(*) as total_steps,
        SUM(CASE WHEN compensation_status = 'uninitialized' THEN 1 ELSE 0 END) as uninitialized,
        SUM(CASE WHEN compensation_status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN compensation_status = 'running' THEN 1 ELSE 0 END) as running,
        SUM(CASE WHEN compensation_status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN compensation_status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN compensation_status = 'delay' THEN 1 ELSE 0 END) as delay_status
    FROM saga_steps;

    -- 服务分布统计
    SELECT
        '=== 服务分布统计 ===' AS section,
        service_name,
        COUNT(*) as step_count,
        AVG(retry_count) as avg_retry_count
    FROM saga_steps
    GROUP BY service_name
    ORDER BY step_count DESC;

    -- 业务场景统计
    SELECT
        '=== 业务场景统计 ===' AS section,
        SUBSTRING_INDEX(name, '_', 1) as business_scenario,
        COUNT(*) as saga_count
    FROM saga_transactions
    GROUP BY SUBSTRING_INDEX(name, '_', 1)
    ORDER BY saga_count DESC;

END$$
DELIMITER ;
