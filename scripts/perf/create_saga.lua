-- wrk 性能测试脚本：Saga 创建接口
-- 使用方法: wrk -t4 -c100 -d30s --script=scripts/perf/create_saga.lua http://localhost:8080/api/saga/create

-- 请求计数器
request_count = 0

-- 初始化函数
function init(args)
    print("开始 Saga 创建接口性能测试...")
end

-- 设置请求
function request()
    request_count = request_count + 1
    
    -- 生成唯一的事务名称
    local saga_name = "性能测试事务_" .. os.time() .. "_" .. request_count
    
    -- 构造请求体
    local body = string.format([[{
        "name": "%s",
        "stepIndexMode": "auto",
        "compensationWindowSec": 300
    }]], saga_name)
    
    -- 设置请求头
    local headers = {}
    headers["Content-Type"] = "application/json"
    
    return wrk.format("POST", nil, headers, body)
end

-- 响应处理
function response(status, headers, body)
    if status ~= 200 then
        print("错误响应: " .. status .. " - " .. body)
    end
end

-- 完成时的统计
function done(summary, latency, requests)
    print("\n=== 性能测试结果 ===")
    print("总请求数: " .. summary.requests)
    print("总耗时: " .. summary.duration / 1000000 .. " 秒")
    print("平均 TPS: " .. summary.requests / (summary.duration / 1000000))
    print("错误数: " .. summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout)
    print("错误率: " .. ((summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout) / summary.requests * 100) .. "%")
    
    print("\n=== 延迟统计 ===")
    print("最小延迟: " .. latency.min / 1000 .. " ms")
    print("最大延迟: " .. latency.max / 1000 .. " ms")
    print("平均延迟: " .. latency.mean / 1000 .. " ms")
    print("P50 延迟: " .. latency:percentile(50) / 1000 .. " ms")
    print("P90 延迟: " .. latency:percentile(90) / 1000 .. " ms")
    print("P95 延迟: " .. latency:percentile(95) / 1000 .. " ms")
    print("P99 延迟: " .. latency:percentile(99) / 1000 .. " ms")
end
