-- =====================================================
-- 快速测试数据生成脚本
-- 用于快速验证和小规模测试
-- =====================================================

USE saga;

-- 生成少量测试数据用于验证
DROP PROCEDURE IF EXISTS GenerateQuickTestData;
DELIMITER $$
CREATE PROCEDURE GenerateQuickTestData()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE saga_id VARCHAR(36);
    DECLARE scenario_names JSON;
    DECLARE scenario_name VARCHAR(100);
    DECLARE step_count INT;
    DECLARE j INT;
    
    -- 定义业务场景
    SET scenario_names = JSON_ARRAY(
        '电商订单处理',
        '金融转账处理', 
        '酒店预订处理',
        '保险理赔处理',
        '通用业务流程'
    );
    
    SELECT '开始生成快速测试数据（100条）...' AS message;
    
    WHILE i <= 100 DO
        -- 生成 saga_id
        SET saga_id = CONCAT(
            SUBSTRING(MD5(CONCAT('quick-test-', i, '-', UNIX_TIMESTAMP(), '-', RAND())), 1, 8), '-',
            SUBSTRING(MD5(CONCAT('quick-test-', i, '-', UNIX_TIMESTAMP(), '-', RAND())), 9, 4), '-',
            '4', SUBSTRING(MD5(CONCAT('quick-test-', i, '-', UNIX_TIMESTAMP(), '-', RAND())), 13, 3), '-',
            SUBSTRING(MD5(CONCAT('quick-test-', i, '-', UNIX_TIMESTAMP(), '-', RAND())), 17, 4), '-',
            SUBSTRING(MD5(CONCAT('quick-test-', i, '-', UNIX_TIMESTAMP(), '-', RAND())), 21, 12)
        );
        
        -- 随机选择场景
        SET scenario_name = JSON_UNQUOTE(JSON_EXTRACT(scenario_names, CONCAT('$[', FLOOR(RAND() * 5), ']')));
        SET step_count = FLOOR(2 + RAND() * 4); -- 2-5个步骤
        
        -- 插入 Saga 事务
        INSERT INTO saga_transactions (
            saga_id, name, saga_status, step_index_mode,
            cur_step_index, compensation_window_sec, created_at, updated_at
        ) VALUES (
            saga_id,
            CONCAT(scenario_name, '_测试_', LPAD(i, 3, '0')),
            CASE FLOOR(RAND() * 5)
                WHEN 0 THEN 'pending'
                WHEN 1 THEN 'running'
                WHEN 2 THEN 'completed'
                WHEN 3 THEN 'compensating'
                ELSE 'failed'
            END,
            IF(RAND() < 0.7, 'auto', 'manual'),
            step_count,
            300,
            NOW() - INTERVAL FLOOR(RAND() * 24) HOUR,
            NOW()
        );
        
        -- 插入步骤
        SET j = 1;
        WHILE j <= step_count DO
            INSERT INTO saga_steps (
                step_id, saga_id, action, step_index, service_name,
                context_data, compensation_context, compensate_endpoint,
                compensation_status, retry_count, created_at, updated_at
            ) VALUES (
                CONCAT('s', LPAD(i, 3, '0'), LPAD(j, 2, '0')),
                saga_id,
                CONCAT('Action', j),
                j,
                CONCAT('service-', j),
                JSON_OBJECT('testData', CONCAT('test-', i, '-', j)),
                JSON_OBJECT('compensateAction', CONCAT('CancelAction', j)),
                CONCAT('http://service-', j, '/compensate'),
                CASE FLOOR(RAND() * 6)
                    WHEN 0 THEN 'uninitialized'
                    WHEN 1 THEN 'pending'
                    WHEN 2 THEN 'running'
                    WHEN 3 THEN 'completed'
                    WHEN 4 THEN 'failed'
                    ELSE 'delay'
                END,
                FLOOR(RAND() * 3),
                NOW() - INTERVAL FLOOR(RAND() * 24) HOUR,
                NOW()
            );
            SET j = j + 1;
        END WHILE;
        
        SET i = i + 1;
    END WHILE;
    
    SELECT '✅ 快速测试数据生成完成！' AS message;
    
    -- 显示统计
    SELECT 
        COUNT(*) as total_sagas,
        SUM(CASE WHEN saga_status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN saga_status = 'running' THEN 1 ELSE 0 END) as running,
        SUM(CASE WHEN saga_status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN saga_status = 'compensating' THEN 1 ELSE 0 END) as compensating,
        SUM(CASE WHEN saga_status = 'failed' THEN 1 ELSE 0 END) as failed
    FROM saga_transactions;
    
    SELECT 
        COUNT(*) as total_steps,
        SUM(CASE WHEN compensation_status = 'pending' THEN 1 ELSE 0 END) as pending_steps,
        SUM(CASE WHEN compensation_status = 'running' THEN 1 ELSE 0 END) as running_steps,
        SUM(CASE WHEN compensation_status = 'completed' THEN 1 ELSE 0 END) as completed_steps,
        SUM(CASE WHEN compensation_status = 'failed' THEN 1 ELSE 0 END) as failed_steps
    FROM saga_steps;
    
END$$
DELIMITER ;

-- 执行快速测试数据生成
-- CALL GenerateQuickTestData();

/*
使用方法：

1. 生成快速测试数据：
   CALL GenerateQuickTestData();

2. 在 Makefile 中使用：
   make db-quick-test

这个脚本会生成100条测试数据，适合：
- 功能验证
- 开发调试
- 快速演示
- 接口测试
*/
