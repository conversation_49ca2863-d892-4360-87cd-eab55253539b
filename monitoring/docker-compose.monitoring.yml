# Saga 分布式事务系统监控栈
# 包含 Prometheus, <PERSON><PERSON>, AlertManager 等监控组件

version: '3.8'

networks:
  saga-monitoring:
    driver: bridge
  saga-network:
    external: true

volumes:
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  alertmanager-data:
    driver: local

services:
  # Prometheus 监控服务
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: saga-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=50GB'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--log.level=info'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./prometheus/saga_alerts.yml:/etc/prometheus/saga_alerts.yml:ro
      - ./prometheus/system_alerts.yml:/etc/prometheus/system_alerts.yml:ro
      - prometheus-data:/prometheus
    networks:
      - saga-monitoring
      - saga-network
    depends_on:
      - alertmanager
    labels:
      - "monitoring.component=prometheus"
      - "monitoring.description=Saga系统指标收集"

  # AlertManager 告警管理
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: saga-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
      - '--log.level=info'
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - ./alertmanager/templates:/etc/alertmanager/templates:ro
      - alertmanager-data:/alertmanager
    networks:
      - saga-monitoring
    labels:
      - "monitoring.component=alertmanager"
      - "monitoring.description=Saga系统告警管理"

  # Grafana 可视化
  grafana:
    image: grafana/grafana:10.0.0
    container_name: saga-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=saga-admin-2023
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
      - GF_SERVER_DOMAIN=localhost
      - GF_SERVER_ROOT_URL=http://localhost:3000
      - GF_SMTP_ENABLED=true
      - GF_SMTP_HOST=smtp.company.com:587
      - GF_SMTP_USER=<EMAIL>
      - GF_SMTP_PASSWORD=grafana-smtp-password
      - GF_SMTP_FROM_ADDRESS=<EMAIL>
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - saga-monitoring
    depends_on:
      - prometheus
    labels:
      - "monitoring.component=grafana"
      - "monitoring.description=Saga系统可视化仪表板"

  # Node Exporter 主机监控
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: saga-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - saga-monitoring
    labels:
      - "monitoring.component=node-exporter"
      - "monitoring.description=主机系统指标收集"

  # cAdvisor 容器监控
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: saga-cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - saga-monitoring
    labels:
      - "monitoring.component=cadvisor"
      - "monitoring.description=容器资源监控"

  # MySQL Exporter 数据库监控
  mysql-exporter:
    image: prom/mysqld-exporter:v0.15.0
    container_name: saga-mysql-exporter
    restart: unless-stopped
    ports:
      - "9104:9104"
    environment:
      - DATA_SOURCE_NAME=root:12345678a@(saga-mysql:3306)/
    command:
      - '--collect.info_schema.processlist'
      - '--collect.info_schema.innodb_metrics'
      - '--collect.info_schema.innodb_tablespaces'
      - '--collect.info_schema.innodb_cmp'
      - '--collect.info_schema.innodb_cmpmem'
      - '--collect.engine_innodb_status'
      - '--collect.binlog_size'
      - '--collect.info_schema.clientstats'
      - '--collect.info_schema.tablestats'
    networks:
      - saga-monitoring
      - saga-network
    depends_on:
      - saga-mysql
    labels:
      - "monitoring.component=mysql-exporter"
      - "monitoring.description=MySQL数据库指标收集"

  # Blackbox Exporter 网络探测
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: saga-blackbox-exporter
    restart: unless-stopped
    ports:
      - "9115:9115"
    volumes:
      - ./blackbox/blackbox.yml:/etc/blackbox_exporter/config.yml:ro
    networks:
      - saga-monitoring
      - saga-network
    labels:
      - "monitoring.component=blackbox-exporter"
      - "monitoring.description=网络连通性探测"

  # Pushgateway 批量任务监控
  pushgateway:
    image: prom/pushgateway:v1.6.0
    container_name: saga-pushgateway
    restart: unless-stopped
    ports:
      - "9091:9091"
    networks:
      - saga-monitoring
    labels:
      - "monitoring.component=pushgateway"
      - "monitoring.description=批量任务指标收集"

  # Jaeger 分布式追踪 (可选)
  jaeger:
    image: jaegertracing/all-in-one:1.46
    container_name: saga-jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - saga-monitoring
    labels:
      - "monitoring.component=jaeger"
      - "monitoring.description=分布式链路追踪"

# 健康检查配置
x-healthcheck-defaults: &healthcheck-defaults
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s

# 重启策略
x-restart-policy: &restart-policy
  restart: unless-stopped

# 日志配置
x-logging-defaults: &logging-defaults
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"
