#!/bin/bash

# Saga 分布式事务系统监控栈部署脚本

set -e

# 配置参数
MONITORING_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_FILE="$MONITORING_DIR/docker-compose.monitoring.yml"
ENV_FILE="$MONITORING_DIR/.env"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p "$MONITORING_DIR/prometheus/data"
    mkdir -p "$MONITORING_DIR/grafana/data"
    mkdir -p "$MONITORING_DIR/alertmanager/data"
    mkdir -p "$MONITORING_DIR/alertmanager/templates"
    mkdir -p "$MONITORING_DIR/blackbox"
    
    # 设置权限
    chmod 777 "$MONITORING_DIR/prometheus/data"
    chmod 777 "$MONITORING_DIR/grafana/data"
    chmod 777 "$MONITORING_DIR/alertmanager/data"
    
    log_success "目录创建完成"
}

# 创建环境变量文件
create_env_file() {
    log_info "创建环境变量文件..."
    
    cat > "$ENV_FILE" << EOF
# Saga 监控栈环境变量

# Grafana 配置
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=saga-admin-2023
GRAFANA_DOMAIN=localhost
GRAFANA_ROOT_URL=http://localhost:3000

# SMTP 配置 (用于告警邮件)
SMTP_HOST=smtp.company.com:587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_FROM=<EMAIL>

# Slack 配置 (用于告警通知)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# 数据库连接 (用于 MySQL Exporter)
MYSQL_HOST=saga-mysql
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=12345678a

# 数据保留策略
PROMETHEUS_RETENTION_TIME=30d
PROMETHEUS_RETENTION_SIZE=50GB

# 网络配置
MONITORING_NETWORK=saga-monitoring
SAGA_NETWORK=saga-network
EOF
    
    log_success "环境变量文件创建完成: $ENV_FILE"
    log_warning "请根据实际情况修改 $ENV_FILE 中的配置"
}

# 创建 Blackbox Exporter 配置
create_blackbox_config() {
    log_info "创建 Blackbox Exporter 配置..."
    
    cat > "$MONITORING_DIR/blackbox/blackbox.yml" << EOF
modules:
  http_2xx:
    prober: http
    timeout: 5s
    http:
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: []
      method: GET
      headers:
        Host: localhost
        Accept-Language: en-US
      no_follow_redirects: false
      fail_if_ssl: false
      fail_if_not_ssl: false
      tls_config:
        insecure_skip_verify: false
      preferred_ip_protocol: "ip4"
      ip_protocol_fallback: false

  http_post_2xx:
    prober: http
    timeout: 5s
    http:
      method: POST
      headers:
        Content-Type: application/json
      body: '{"test": true}'

  tcp_connect:
    prober: tcp
    timeout: 5s

  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: "ip4"
      source_ip_address: "127.0.0.1"
EOF
    
    log_success "Blackbox Exporter 配置创建完成"
}

# 验证配置文件
validate_configs() {
    log_info "验证配置文件..."
    
    # 验证 Prometheus 配置
    if [ -f "$MONITORING_DIR/prometheus/prometheus.yml" ]; then
        log_success "Prometheus 配置文件存在"
    else
        log_error "Prometheus 配置文件不存在: $MONITORING_DIR/prometheus/prometheus.yml"
        exit 1
    fi
    
    # 验证 AlertManager 配置
    if [ -f "$MONITORING_DIR/alertmanager/alertmanager.yml" ]; then
        log_success "AlertManager 配置文件存在"
    else
        log_error "AlertManager 配置文件不存在: $MONITORING_DIR/alertmanager/alertmanager.yml"
        exit 1
    fi
    
    # 验证告警规则
    if [ -f "$MONITORING_DIR/prometheus/saga_alerts.yml" ]; then
        log_success "Saga 告警规则文件存在"
    else
        log_error "Saga 告警规则文件不存在: $MONITORING_DIR/prometheus/saga_alerts.yml"
        exit 1
    fi
    
    log_success "配置文件验证通过"
}

# 部署监控栈
deploy_monitoring() {
    log_info "部署监控栈..."
    
    cd "$MONITORING_DIR"
    
    # 拉取镜像
    log_info "拉取 Docker 镜像..."
    docker-compose -f "$COMPOSE_FILE" pull
    
    # 启动服务
    log_info "启动监控服务..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose -f "$COMPOSE_FILE" ps
    
    log_success "监控栈部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查 Prometheus
    if curl -s http://localhost:9090/-/healthy > /dev/null; then
        log_success "Prometheus 运行正常 (http://localhost:9090)"
    else
        log_error "Prometheus 健康检查失败"
    fi
    
    # 检查 Grafana
    if curl -s http://localhost:3000/api/health > /dev/null; then
        log_success "Grafana 运行正常 (http://localhost:3000)"
    else
        log_error "Grafana 健康检查失败"
    fi
    
    # 检查 AlertManager
    if curl -s http://localhost:9093/-/healthy > /dev/null; then
        log_success "AlertManager 运行正常 (http://localhost:9093)"
    else
        log_error "AlertManager 健康检查失败"
    fi
    
    log_success "部署验证完成"
}

# 显示访问信息
show_access_info() {
    log_info "监控系统访问信息:"
    echo ""
    echo "🔍 Prometheus:   http://localhost:9090"
    echo "📊 Grafana:      http://localhost:3000 (admin/saga-admin-2023)"
    echo "🚨 AlertManager: http://localhost:9093"
    echo "📈 Node Exporter: http://localhost:9100"
    echo "🐳 cAdvisor:     http://localhost:8080"
    echo "🔍 Jaeger:       http://localhost:16686"
    echo ""
    log_info "请根据需要访问相应的监控界面"
}

# 主函数
main() {
    log_info "开始部署 Saga 分布式事务系统监控栈"
    
    check_dependencies
    create_directories
    create_env_file
    create_blackbox_config
    validate_configs
    deploy_monitoring
    verify_deployment
    show_access_info
    
    log_success "监控栈部署完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
