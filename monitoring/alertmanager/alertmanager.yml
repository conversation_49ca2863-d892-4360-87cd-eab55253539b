# AlertManager 配置文件 - Saga 分布式事务系统

global:
  # SMTP 配置
  smtp_smarthost: 'smtp.company.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-smtp-password'
  smtp_require_tls: true

  # Slack 全局配置
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'

# 模板配置
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# 路由配置
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  
  routes:
    # 严重告警路由
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      group_interval: 5s
      repeat_interval: 30m
      continue: true

    # 业务告警路由
    - match:
        category: business
      receiver: 'business-alerts'
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 2h

    # 性能告警路由
    - match:
        category: performance
      receiver: 'performance-alerts'
      group_wait: 1m
      group_interval: 5m
      repeat_interval: 1h

    # 数据库告警路由
    - match:
        category: database
      receiver: 'database-alerts'
      group_wait: 30s
      group_interval: 2m
      repeat_interval: 1h

    # 系统资源告警路由
    - match:
        category: system
      receiver: 'system-alerts'
      group_wait: 2m
      group_interval: 10m
      repeat_interval: 4h

    # 监控系统告警路由
    - match:
        category: monitoring
      receiver: 'monitoring-alerts'
      group_wait: 5m
      group_interval: 30m
      repeat_interval: 12h

# 抑制规则
inhibit_rules:
  # 当服务不可用时，抑制其他相关告警
  - source_match:
      alertname: SagaServiceDown
    target_match:
      service: saga
    equal: ['instance']

  # 当主机不可用时，抑制容器告警
  - source_match:
      alertname: HostDown
    target_match:
      category: container
    equal: ['instance']

  # 当数据库不可用时，抑制数据库相关告警
  - source_match:
      alertname: MySQLDown
    target_match:
      category: database
    equal: ['instance']

  # 严重告警抑制警告告警
  - source_match:
      severity: critical
    target_match:
      severity: warning
    equal: ['alertname', 'instance']

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default-receiver'
    slack_configs:
      - channel: '#saga-alerts'
        title: 'Saga 系统告警'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *严重程度*: {{ .Labels.severity }}
          *服务*: {{ .Labels.service }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true

  # 严重告警接收器
  - name: 'critical-alerts'
    slack_configs:
      - channel: '#saga-critical'
        title: '🚨 Saga 严重告警'
        text: |
          {{ range .Alerts }}
          🚨 *严重告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *服务*: {{ .Labels.service }}
          *实例*: {{ .Labels.instance }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ if .Annotations.runbook_url }}*处理手册*: {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
        send_resolved: true
        color: 'danger'
    
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 Saga 严重告警: {{ .GroupLabels.alertname }}'
        body: |
          严重告警详情:
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          实例: {{ .Labels.instance }}
          开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ if .Annotations.runbook_url }}处理手册: {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}

    # 短信告警 (需要配置短信网关)
    webhook_configs:
      - url: 'http://sms-gateway:8080/send'
        send_resolved: false
        http_config:
          basic_auth:
            username: 'saga-alerts'
            password: 'sms-gateway-password'

  # 业务告警接收器
  - name: 'business-alerts'
    slack_configs:
      - channel: '#saga-business'
        title: '📊 Saga 业务告警'
        text: |
          {{ range .Alerts }}
          📊 *业务告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *服务*: {{ .Labels.service }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true
        color: 'warning'

  # 性能告警接收器
  - name: 'performance-alerts'
    slack_configs:
      - channel: '#saga-performance'
        title: '⚡ Saga 性能告警'
        text: |
          {{ range .Alerts }}
          ⚡ *性能告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *服务*: {{ .Labels.service }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true
        color: '#ff9900'

  # 数据库告警接收器
  - name: 'database-alerts'
    slack_configs:
      - channel: '#saga-database'
        title: '🗄️ Saga 数据库告警'
        text: |
          {{ range .Alerts }}
          🗄️ *数据库告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *实例*: {{ .Labels.instance }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true
        color: '#9900ff'

  # 系统告警接收器
  - name: 'system-alerts'
    slack_configs:
      - channel: '#saga-system'
        title: '🖥️ Saga 系统告警'
        text: |
          {{ range .Alerts }}
          🖥️ *系统告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *主机*: {{ .Labels.instance }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true
        color: '#0099ff'

  # 监控系统告警接收器
  - name: 'monitoring-alerts'
    slack_configs:
      - channel: '#saga-monitoring'
        title: '📈 监控系统告警'
        text: |
          {{ range .Alerts }}
          📈 *监控告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *组件*: {{ .Labels.job }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true
        color: 'good'

# 时间窗口配置
time_intervals:
  # 工作时间
  - name: 'business-hours'
    time_intervals:
      - times:
          - start_time: '09:00'
            end_time: '18:00'
        weekdays: ['monday:friday']
        location: 'Asia/Shanghai'

  # 非工作时间
  - name: 'off-hours'
    time_intervals:
      - times:
          - start_time: '18:00'
            end_time: '09:00'
        weekdays: ['monday:friday']
        location: 'Asia/Shanghai'
      - weekdays: ['saturday', 'sunday']
        location: 'Asia/Shanghai'
