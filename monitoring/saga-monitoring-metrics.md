# Saga 分布式事务系统监控指标定义

## 概述

本文档定义了 Saga 分布式事务系统的关键监控指标、告警阈值和监控策略，基于性能测试结果设计。

## 1. 核心性能指标

### 1.1 吞吐量指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_transactions_created_rate` | 事务创建速率 (QPS) | 3,400 | < 2,700 | < 1,700 | 1分钟 |
| `saga_transactions_queried_rate` | 事务查询速率 (QPS) | 6,400 | < 5,100 | < 3,200 | 1分钟 |
| `saga_compensations_reported_rate` | 补偿上报速率 (QPS) | 6,000 | < 4,800 | < 3,000 | 1分钟 |
| `saga_transactions_committed_rate` | 事务提交速率 (QPS) | 6,000 | < 4,800 | < 3,000 | 1分钟 |
| `saga_transactions_rollback_rate` | 事务回滚速率 (QPS) | 6,300 | < 5,000 | < 3,100 | 1分钟 |

### 1.2 延迟指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_transactions_created_latency_p99` | 事务创建 P99 延迟 | 18ms | > 30ms | > 50ms | 1分钟 |
| `saga_transactions_queried_latency_p99` | 事务查询 P99 延迟 | 44ms | > 60ms | > 100ms | 1分钟 |
| `saga_compensations_reported_latency_p99` | 补偿上报 P99 延迟 | 43ms | > 60ms | > 100ms | 1分钟 |
| `saga_transactions_committed_latency_p99` | 事务提交 P99 延迟 | 40ms | > 60ms | > 100ms | 1分钟 |
| `saga_transactions_rollback_latency_p99` | 事务回滚 P99 延迟 | 46ms | > 60ms | > 100ms | 1分钟 |
| `saga_api_latency_avg` | API 平均延迟 | 5ms | > 10ms | > 20ms | 30秒 |

## 2. 系统资源指标

### 2.1 应用资源指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_app_cpu_usage` | 应用 CPU 使用率 | < 30% | > 70% | > 90% | 30秒 |
| `saga_app_memory_usage` | 应用内存使用量 | 140MB | > 500MB | > 1GB | 30秒 |
| `saga_app_memory_usage_percent` | 应用内存使用率 | < 5% | > 70% | > 90% | 30秒 |
| `saga_app_goroutines_count` | Go 协程数量 | < 1000 | > 5000 | > 10000 | 1分钟 |
| `saga_app_gc_pause_time` | GC 暂停时间 | < 10ms | > 50ms | > 100ms | 1分钟 |
| `saga_app_open_connections` | 打开连接数 | < 100 | > 500 | > 1000 | 1分钟 |

### 2.2 数据库资源指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_db_cpu_usage` | 数据库 CPU 使用率 | < 40% | > 70% | > 90% | 30秒 |
| `saga_db_memory_usage` | 数据库内存使用量 | 1.6GB | > 5GB | > 7GB | 30秒 |
| `saga_db_memory_usage_percent` | 数据库内存使用率 | < 25% | > 70% | > 90% | 30秒 |
| `saga_db_connections` | 数据库连接数 | < 100 | > 300 | > 500 | 1分钟 |
| `saga_db_slow_queries` | 慢查询数量 | 0 | > 5 | > 20 | 5分钟 |
| `saga_db_query_time_avg` | 平均查询时间 | < 5ms | > 20ms | > 50ms | 1分钟 |

## 3. 业务指标

### 3.1 事务状态指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_transactions_pending_count` | 待处理事务数量 | < 100 | > 1000 | > 5000 | 1分钟 |
| `saga_transactions_running_count` | 运行中事务数量 | < 500 | > 2000 | > 10000 | 1分钟 |
| `saga_transactions_compensating_count` | 补偿中事务数量 | < 10 | > 100 | > 500 | 1分钟 |
| `saga_transactions_failed_count` | 失败事务数量 | < 5 | > 50 | > 200 | 1分钟 |
| `saga_transactions_timeout_count` | 超时事务数量 | 0 | > 10 | > 50 | 5分钟 |

### 3.2 补偿操作指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_compensation_success_rate` | 补偿成功率 | > 99.9% | < 99% | < 95% | 5分钟 |
| `saga_compensation_retry_count` | 补偿重试次数 | < 10 | > 50 | > 200 | 5分钟 |
| `saga_compensation_timeout_count` | 补偿超时次数 | 0 | > 5 | > 20 | 5分钟 |

### 3.3 错误率指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_api_error_rate` | API 错误率 | < 0.1% | > 1% | > 5% | 1分钟 |
| `saga_db_error_rate` | 数据库错误率 | < 0.01% | > 0.1% | > 1% | 1分钟 |
| `saga_compensation_error_rate` | 补偿操作错误率 | < 0.1% | > 1% | > 5% | 5分钟 |

## 4. 可用性指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_service_availability` | 服务可用性 | > 99.99% | < 99.9% | < 99.5% | 5分钟 |
| `saga_api_success_rate` | API 成功率 | > 99.9% | < 99.5% | < 99% | 1分钟 |
| `saga_health_check_latency` | 健康检查延迟 | < 1ms | > 10ms | > 50ms | 30秒 |

## 5. 自定义业务指标

| 指标名称 | 描述 | 基准值 | 警告阈值 | 严重阈值 | 监控频率 |
|---------|------|--------|----------|----------|----------|
| `saga_auto_mode_transactions_rate` | Auto模式事务创建速率 | 3,400 | < 2,700 | < 1,700 | 5分钟 |
| `saga_manual_mode_transactions_rate` | Manual模式事务创建速率 | 3,100 | < 2,400 | < 1,500 | 5分钟 |
| `saga_step_templates_count_avg` | 平均步骤模板数量 | < 5 | > 20 | > 50 | 15分钟 |
| `saga_transaction_completion_time_avg` | 事务平均完成时间 | < 1s | > 5s | > 30s | 5分钟 |

## 6. 监控维度

所有指标应支持以下维度进行分组和筛选：

1. **环境维度**: `production`, `staging`, `testing`
2. **服务实例维度**: `instance_id`, `pod_name`, `node_name`
3. **API维度**: `api_endpoint`, `http_method`
4. **事务维度**: `saga_id`, `step_index_mode`
5. **客户端维度**: `client_id`, `user_agent`
6. **地域维度**: `region`, `availability_zone`

## 7. 数据保留策略

| 数据类型 | 聚合粒度 | 保留时间 |
|---------|----------|----------|
| 原始指标数据 | 10秒 | 7天 |
| 1分钟聚合数据 | 1分钟 | 30天 |
| 1小时聚合数据 | 1小时 | 90天 |
| 1天聚合数据 | 1天 | 1年 |

## 8. 告警通知渠道

1. **紧急告警**: Slack + 短信 + 电话
2. **警告告警**: Slack + 邮件
3. **信息告警**: Slack

## 9. 告警抑制策略

1. **重复告警抑制**: 相同告警5分钟内最多发送1次
2. **级联告警抑制**: 当高级别告警触发时，抑制相关的低级别告警
3. **维护窗口抑制**: 在计划维护窗口期间抑制非紧急告警
