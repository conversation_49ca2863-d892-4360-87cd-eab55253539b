# Prometheus 配置文件 - Saga 分布式事务系统监控

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'saga-cluster'
    environment: 'production'

# 告警规则文件
rule_files:
  - "saga_alerts.yml"
  - "system_alerts.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Saga 应用监控
  - job_name: 'saga-app'
    static_configs:
      - targets: ['saga-app:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__meta_docker_container_name]
        target_label: container_name
    metric_relabel_configs:
      # 重命名指标以符合命名规范
      - source_labels: [__name__]
        regex: 'go_(.+)'
        target_label: __name__
        replacement: 'saga_go_${1}'

  # MySQL 数据库监控
  - job_name: 'saga-mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 30s
    scrape_timeout: 10s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'saga-mysql'

  # 容器监控 (cAdvisor)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metric_relabel_configs:
      # 只保留 Saga 相关容器的指标
      - source_labels: [container_label_com_docker_compose_service]
        regex: 'saga-.*'
        action: keep

  # 节点监控 (Node Exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Prometheus 自监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s

  # Grafana 监控
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 60s

# 远程写入配置 (可选，用于长期存储)
# remote_write:
#   - url: "http://thanos-receive:19291/api/v1/receive"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 存储配置
storage:
  tsdb:
    path: /prometheus/data
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true

# 查询配置
query:
  timeout: 2m
  max_concurrent_queries: 20
  max_samples: 50000000
