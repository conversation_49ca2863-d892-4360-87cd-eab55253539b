# Saga 分布式事务系统告警规则

groups:
  # 性能告警组
  - name: saga_performance_alerts
    rules:
      # API 吞吐量告警
      - alert: SagaAPIThroughputLow
        expr: rate(saga_api_requests_total[5m]) < 2000
        for: 2m
        labels:
          severity: warning
          service: saga
          category: performance
        annotations:
          summary: "Saga API 吞吐量过低"
          description: "Saga API 5分钟平均 QPS {{ $value }} 低于预期阈值 2000"
          runbook_url: "https://wiki.company.com/saga/runbooks/low-throughput"

      - alert: SagaAPIThroughputCritical
        expr: rate(saga_api_requests_total[5m]) < 1000
        for: 1m
        labels:
          severity: critical
          service: saga
          category: performance
        annotations:
          summary: "Saga API 吞吐量严重过低"
          description: "Saga API 5分钟平均 QPS {{ $value }} 严重低于预期"

      # API 延迟告警
      - alert: SagaAPILatencyHigh
        expr: histogram_quantile(0.99, rate(saga_api_request_duration_seconds_bucket[5m])) > 0.05
        for: 3m
        labels:
          severity: warning
          service: saga
          category: performance
        annotations:
          summary: "Saga API 延迟过高"
          description: "Saga API P99 延迟 {{ $value }}s 超过 50ms"

      - alert: SagaAPILatencyCritical
        expr: histogram_quantile(0.99, rate(saga_api_request_duration_seconds_bucket[5m])) > 0.1
        for: 1m
        labels:
          severity: critical
          service: saga
          category: performance
        annotations:
          summary: "Saga API 延迟严重过高"
          description: "Saga API P99 延迟 {{ $value }}s 超过 100ms"

  # 错误率告警组
  - name: saga_error_alerts
    rules:
      # API 错误率告警
      - alert: SagaAPIErrorRateHigh
        expr: rate(saga_api_requests_total{status=~"5.."}[5m]) / rate(saga_api_requests_total[5m]) > 0.01
        for: 2m
        labels:
          severity: warning
          service: saga
          category: error
        annotations:
          summary: "Saga API 错误率过高"
          description: "Saga API 5分钟错误率 {{ $value | humanizePercentage }} 超过 1%"

      - alert: SagaAPIErrorRateCritical
        expr: rate(saga_api_requests_total{status=~"5.."}[5m]) / rate(saga_api_requests_total[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
          service: saga
          category: error
        annotations:
          summary: "Saga API 错误率严重过高"
          description: "Saga API 5分钟错误率 {{ $value | humanizePercentage }} 超过 5%"

      # 数据库错误告警
      - alert: SagaDatabaseErrorHigh
        expr: rate(saga_db_errors_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: saga
          category: database
        annotations:
          summary: "Saga 数据库错误过多"
          description: "Saga 数据库 5分钟错误数 {{ $value }} 超过阈值"

  # 业务指标告警组
  - name: saga_business_alerts
    rules:
      # 待处理事务堆积告警
      - alert: SagaPendingTransactionsHigh
        expr: saga_transactions_pending_count > 1000
        for: 5m
        labels:
          severity: warning
          service: saga
          category: business
        annotations:
          summary: "Saga 待处理事务堆积"
          description: "待处理事务数量 {{ $value }} 超过 1000"

      - alert: SagaPendingTransactionsCritical
        expr: saga_transactions_pending_count > 5000
        for: 2m
        labels:
          severity: critical
          service: saga
          category: business
        annotations:
          summary: "Saga 待处理事务严重堆积"
          description: "待处理事务数量 {{ $value }} 超过 5000"

      # 补偿成功率告警
      - alert: SagaCompensationSuccessRateLow
        expr: saga_compensation_success_rate < 0.99
        for: 5m
        labels:
          severity: warning
          service: saga
          category: business
        annotations:
          summary: "Saga 补偿成功率过低"
          description: "补偿成功率 {{ $value | humanizePercentage }} 低于 99%"

      - alert: SagaCompensationSuccessRateCritical
        expr: saga_compensation_success_rate < 0.95
        for: 2m
        labels:
          severity: critical
          service: saga
          category: business
        annotations:
          summary: "Saga 补偿成功率严重过低"
          description: "补偿成功率 {{ $value | humanizePercentage }} 低于 95%"

      # 事务超时告警
      - alert: SagaTransactionTimeoutHigh
        expr: increase(saga_transactions_timeout_count[10m]) > 10
        for: 0m
        labels:
          severity: warning
          service: saga
          category: business
        annotations:
          summary: "Saga 事务超时过多"
          description: "10分钟内事务超时数量 {{ $value }} 超过 10"

  # 资源使用告警组
  - name: saga_resource_alerts
    rules:
      # 应用内存使用告警
      - alert: SagaAppMemoryUsageHigh
        expr: saga_app_memory_usage_bytes / saga_app_memory_limit_bytes > 0.7
        for: 5m
        labels:
          severity: warning
          service: saga
          category: resource
        annotations:
          summary: "Saga 应用内存使用率过高"
          description: "应用内存使用率 {{ $value | humanizePercentage }} 超过 70%"

      - alert: SagaAppMemoryUsageCritical
        expr: saga_app_memory_usage_bytes / saga_app_memory_limit_bytes > 0.9
        for: 2m
        labels:
          severity: critical
          service: saga
          category: resource
        annotations:
          summary: "Saga 应用内存使用率严重过高"
          description: "应用内存使用率 {{ $value | humanizePercentage }} 超过 90%"

      # 应用 CPU 使用告警
      - alert: SagaAppCPUUsageHigh
        expr: rate(saga_app_cpu_usage_seconds_total[5m]) > 0.7
        for: 5m
        labels:
          severity: warning
          service: saga
          category: resource
        annotations:
          summary: "Saga 应用 CPU 使用率过高"
          description: "应用 CPU 使用率 {{ $value | humanizePercentage }} 超过 70%"

      # 数据库连接数告警
      - alert: SagaDatabaseConnectionsHigh
        expr: saga_db_connections_active > 300
        for: 3m
        labels:
          severity: warning
          service: saga
          category: database
        annotations:
          summary: "Saga 数据库连接数过高"
          description: "数据库活跃连接数 {{ $value }} 超过 300"

      # Go 协程数量告警
      - alert: SagaGoroutinesHigh
        expr: saga_go_goroutines > 5000
        for: 5m
        labels:
          severity: warning
          service: saga
          category: resource
        annotations:
          summary: "Saga Go 协程数量过高"
          description: "Go 协程数量 {{ $value }} 超过 5000"

  # 可用性告警组
  - name: saga_availability_alerts
    rules:
      # 服务不可用告警
      - alert: SagaServiceDown
        expr: up{job="saga-app"} == 0
        for: 1m
        labels:
          severity: critical
          service: saga
          category: availability
        annotations:
          summary: "Saga 服务不可用"
          description: "Saga 服务实例 {{ $labels.instance }} 不可用"

      # 健康检查失败告警
      - alert: SagaHealthCheckFailed
        expr: saga_health_check_status != 1
        for: 2m
        labels:
          severity: critical
          service: saga
          category: availability
        annotations:
          summary: "Saga 健康检查失败"
          description: "Saga 健康检查失败，服务可能存在问题"

      # 数据库连接失败告警
      - alert: SagaDatabaseConnectionFailed
        expr: saga_db_connection_status != 1
        for: 1m
        labels:
          severity: critical
          service: saga
          category: database
        annotations:
          summary: "Saga 数据库连接失败"
          description: "Saga 无法连接到数据库"
