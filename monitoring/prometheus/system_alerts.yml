# 系统级别告警规则

groups:
  # 主机资源告警组
  - name: host_resource_alerts
    rules:
      # CPU 使用率告警
      - alert: HostCPUUsageHigh
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "主机 CPU 使用率过高"
          description: "主机 {{ $labels.instance }} CPU 使用率 {{ $value }}% 超过 80%"

      - alert: HostCPUUsageCritical
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 95
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "主机 CPU 使用率严重过高"
          description: "主机 {{ $labels.instance }} CPU 使用率 {{ $value }}% 超过 95%"

      # 内存使用率告警
      - alert: HostMemoryUsageHigh
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "主机内存使用率过高"
          description: "主机 {{ $labels.instance }} 内存使用率 {{ $value }}% 超过 80%"

      - alert: HostMemoryUsageCritical
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 95
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "主机内存使用率严重过高"
          description: "主机 {{ $labels.instance }} 内存使用率 {{ $value }}% 超过 95%"

      # 磁盘使用率告警
      - alert: HostDiskUsageHigh
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "主机磁盘使用率过高"
          description: "主机 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率 {{ $value }}% 超过 80%"

      - alert: HostDiskUsageCritical
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 95
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "主机磁盘使用率严重过高"
          description: "主机 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率 {{ $value }}% 超过 95%"

  # 容器资源告警组
  - name: container_resource_alerts
    rules:
      # 容器 CPU 使用率告警
      - alert: ContainerCPUUsageHigh
        expr: rate(container_cpu_usage_seconds_total{container_label_com_docker_compose_service=~"saga-.*"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: container
        annotations:
          summary: "容器 CPU 使用率过高"
          description: "容器 {{ $labels.name }} CPU 使用率 {{ $value }}% 超过 80%"

      # 容器内存使用率告警
      - alert: ContainerMemoryUsageHigh
        expr: (container_memory_usage_bytes{container_label_com_docker_compose_service=~"saga-.*"} / container_spec_memory_limit_bytes) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: container
        annotations:
          summary: "容器内存使用率过高"
          description: "容器 {{ $labels.name }} 内存使用率 {{ $value }}% 超过 80%"

      # 容器重启告警
      - alert: ContainerRestarted
        expr: increase(container_start_time_seconds{container_label_com_docker_compose_service=~"saga-.*"}[10m]) > 0
        for: 0m
        labels:
          severity: warning
          category: container
        annotations:
          summary: "容器重启"
          description: "容器 {{ $labels.name }} 在过去 10 分钟内重启了 {{ $value }} 次"

  # 数据库告警组
  - name: database_alerts
    rules:
      # MySQL 连接数告警
      - alert: MySQLConnectionsHigh
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "MySQL 连接数过高"
          description: "MySQL 连接使用率 {{ $value }}% 超过 80%"

      # MySQL 慢查询告警
      - alert: MySQLSlowQueriesHigh
        expr: rate(mysql_global_status_slow_queries[5m]) > 5
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "MySQL 慢查询过多"
          description: "MySQL 5分钟慢查询数量 {{ $value }} 超过 5"

      # MySQL 复制延迟告警
      - alert: MySQLReplicationLag
        expr: mysql_slave_lag_seconds > 30
        for: 2m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "MySQL 复制延迟过高"
          description: "MySQL 复制延迟 {{ $value }}s 超过 30s"

      # MySQL 不可用告警
      - alert: MySQLDown
        expr: mysql_up == 0
        for: 1m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "MySQL 服务不可用"
          description: "MySQL 服务不可用"

  # 网络告警组
  - name: network_alerts
    rules:
      # 网络延迟告警
      - alert: HighNetworkLatency
        expr: probe_duration_seconds > 0.1
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "网络延迟过高"
          description: "网络延迟 {{ $value }}s 超过 100ms"

      # 网络丢包告警
      - alert: NetworkPacketLoss
        expr: probe_success == 0
        for: 2m
        labels:
          severity: critical
          category: network
        annotations:
          summary: "网络连接失败"
          description: "网络探测失败，可能存在网络问题"

  # 监控系统自身告警组
  - name: monitoring_alerts
    rules:
      # Prometheus 目标不可达告警
      - alert: PrometheusTargetDown
        expr: up == 0
        for: 5m
        labels:
          severity: warning
          category: monitoring
        annotations:
          summary: "Prometheus 目标不可达"
          description: "Prometheus 无法抓取目标 {{ $labels.job }}/{{ $labels.instance }}"

      # Prometheus 配置重载失败告警
      - alert: PrometheusConfigReloadFailed
        expr: prometheus_config_last_reload_successful != 1
        for: 5m
        labels:
          severity: warning
          category: monitoring
        annotations:
          summary: "Prometheus 配置重载失败"
          description: "Prometheus 配置重载失败"

      # AlertManager 不可用告警
      - alert: AlertManagerDown
        expr: up{job="alertmanager"} == 0
        for: 5m
        labels:
          severity: critical
          category: monitoring
        annotations:
          summary: "AlertManager 不可用"
          description: "AlertManager 服务不可用"
