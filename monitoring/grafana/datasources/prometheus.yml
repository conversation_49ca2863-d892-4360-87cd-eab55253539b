# Grafana 数据源配置

apiVersion: 1

datasources:
  # Prometheus 主数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.45.0
      cacheLevel: 'High'
      disableMetricsLookup: false
      customQueryParameters: ''
      timeInterval: '15s'
      queryTimeout: '60s'
      defaultRegion: 'default'
      tlsSkipVerify: false
    secureJsonData: {}

  # AlertManager 数据源
  - name: AlertManager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    editable: true
    jsonData:
      implementation: prometheus
      handleGrafanaManagedAlerts: false
    secureJsonData: {}

  # Jaeger 追踪数据源 (可选)
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: 'prometheus'
        tags: ['job', 'instance', 'pod', 'namespace']
        mappedTags: [
          {key: 'service.name', value: 'service'},
          {key: 'service.namespace', value: 'namespace'}
        ]
        mapTagNamesEnabled: true
        spanStartTimeShift: '1h'
        spanEndTimeShift: '1h'
        filterByTraceID: false
        filterBySpanID: false
      tracesToMetrics:
        datasourceUid: 'prometheus'
        tags: [
          {key: 'service.name', value: 'service'},
          {key: 'job'}
        ]
        queries: [
          {
            name: 'Sample query',
            query: 'sum(rate(traces_spanmetrics_latency_bucket{$$__tags}[5m]))'
          }
        ]
      nodeGraph:
        enabled: true
      search:
        hide: false
      spanBar:
        type: 'Tag'
        tag: 'http.path'
    secureJsonData: {}
