{"dashboard": {"id": null, "title": "Saga 分布式事务系统 - 总览", "tags": ["saga", "overview"], "style": "dark", "timezone": "Asia/Shanghai", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "系统状态概览", "type": "stat", "gridPos": {"h": 4, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up{job=\"saga-app\"}", "legendFormat": "服务状态", "refId": "A"}, {"expr": "saga_health_check_status", "legendFormat": "健康检查", "refId": "B"}, {"expr": "saga_db_connection_status", "legendFormat": "数据库连接", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "离线"}}, "type": "value"}, {"options": {"1": {"text": "在线"}}, "type": "value"}]}}}, {"id": 2, "title": "API 吞吐量 (QPS)", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "targets": [{"expr": "rate(saga_api_requests_total[5m])", "legendFormat": "总 QPS", "refId": "A"}, {"expr": "rate(saga_api_requests_total{endpoint=\"/saga/transactions\", method=\"POST\"}[5m])", "legendFormat": "事务创建", "refId": "B"}, {"expr": "rate(saga_api_requests_total{endpoint=\"/saga/transactions/compensation\"}[5m])", "legendFormat": "补偿上报", "refId": "C"}, {"expr": "rate(saga_api_requests_total{endpoint=\"/saga/transactions/commit\"}[5m])", "legendFormat": "事务提交", "refId": "D"}], "yAxes": [{"label": "QPS", "min": 0}, {"show": false}], "alert": {"conditions": [{"evaluator": {"params": [2000], "type": "lt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "2m", "frequency": "10s", "handler": 1, "name": "API 吞吐量过低", "noDataState": "no_data", "notifications": []}}, {"id": 3, "title": "API 延迟分布", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "targets": [{"expr": "histogram_quantile(0.50, rate(saga_api_request_duration_seconds_bucket[5m]))", "legendFormat": "P50", "refId": "A"}, {"expr": "histogram_quantile(0.90, rate(saga_api_request_duration_seconds_bucket[5m]))", "legendFormat": "P90", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(saga_api_request_duration_seconds_bucket[5m]))", "legendFormat": "P99", "refId": "C"}], "yAxes": [{"label": "延迟 (秒)", "min": 0}, {"show": false}]}, {"id": 4, "title": "事务状态分布", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 12}, "targets": [{"expr": "saga_transactions_pending_count", "legendFormat": "待处理", "refId": "A"}, {"expr": "saga_transactions_running_count", "legendFormat": "运行中", "refId": "B"}, {"expr": "saga_transactions_completed_count", "legendFormat": "已完成", "refId": "C"}, {"expr": "saga_transactions_failed_count", "legendFormat": "失败", "refId": "D"}, {"expr": "saga_transactions_compensating_count", "legendFormat": "补偿中", "refId": "E"}]}, {"id": 5, "title": "错误率", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 12}, "targets": [{"expr": "rate(saga_api_requests_total{status=~\"5..\"}[5m]) / rate(saga_api_requests_total[5m]) * 100", "legendFormat": "API 错误率 (%)", "refId": "A"}, {"expr": "rate(saga_db_errors_total[5m]) / rate(saga_db_operations_total[5m]) * 100", "legendFormat": "数据库错误率 (%)", "refId": "B"}], "yAxes": [{"label": "错误率 (%)", "min": 0, "max": 100}, {"show": false}]}, {"id": 6, "title": "资源使用情况", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 12}, "targets": [{"expr": "saga_app_memory_usage_bytes / saga_app_memory_limit_bytes * 100", "legendFormat": "应用内存使用率 (%)", "refId": "A"}, {"expr": "rate(saga_app_cpu_usage_seconds_total[5m]) * 100", "legendFormat": "应用 CPU 使用率 (%)", "refId": "B"}, {"expr": "saga_db_memory_usage_bytes / saga_db_memory_limit_bytes * 100", "legendFormat": "数据库内存使用率 (%)", "refId": "C"}], "yAxes": [{"label": "使用率 (%)", "min": 0, "max": 100}, {"show": false}]}, {"id": 7, "title": "补偿操作统计", "type": "graph", "gridPos": {"h": 6, "w": 12, "x": 0, "y": 20}, "targets": [{"expr": "saga_compensation_success_rate * 100", "legendFormat": "补偿成功率 (%)", "refId": "A"}, {"expr": "rate(saga_compensation_retry_count[5m])", "legendFormat": "补偿重试率", "refId": "B"}, {"expr": "rate(saga_compensation_timeout_count[5m])", "legendFormat": "补偿超时率", "refId": "C"}]}, {"id": 8, "title": "数据库连接池状态", "type": "graph", "gridPos": {"h": 6, "w": 12, "x": 12, "y": 20}, "targets": [{"expr": "saga_db_connections_active", "legendFormat": "活跃连接", "refId": "A"}, {"expr": "saga_db_connections_idle", "legendFormat": "空闲连接", "refId": "B"}, {"expr": "saga_db_connections_max", "legendFormat": "最大连接数", "refId": "C"}]}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"saga-app\"}, instance)", "refresh": 1, "includeAll": true, "allValue": ".*"}, {"name": "interval", "type": "interval", "query": "1m,5m,10m,30m,1h", "current": {"text": "5m", "value": "5m"}}]}, "annotations": {"list": [{"name": "部署事件", "datasource": "prometheus", "expr": "changes(saga_app_start_time[1h]) > 0", "titleFormat": "应用重启", "textFormat": "Saga 应用在 {{$labels.instance}} 上重启"}]}}}