# Saga 分布式事务系统 Makefile
# 优化版本 - 删除多余命令，保留核心功能

# =============================================================================
# 项目配置
# =============================================================================
PROJECT_NAME = saga
BINARY_NAME = saga
DOCKER_NAME = saga-transaction-system

# Go 相关配置
GO_VERSION = 1.23.0
GOOS = $(shell go env GOOS)
GOARCH = $(shell go env GOARCH)
CGO_ENABLED = 0

# 版本信息
VERSION = $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME = $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建标志
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT) -w -s"

# 默认目标
.DEFAULT_GOAL := help

# =============================================================================
# 帮助信息
# =============================================================================

.PHONY: help
help: ## 显示帮助信息
	@echo "Saga 分布式事务系统 - 可用命令:"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""

# =============================================================================
# 构建相关
# =============================================================================

.PHONY: build
build: ## 构建应用程序
	@echo "构建应用程序..."
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) go build $(LDFLAGS) -o $(BINARY_NAME) main.go
	@echo "构建完成: $(BINARY_NAME)"

.PHONY: build-linux
build-linux: ## 构建 Linux 版本
	@echo "构建 Linux 版本..."
	@CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BINARY_NAME) main.go
	@echo "构建完成: $(BINARY_NAME) (Linux AMD64)"

.PHONY: clean
clean: ## 清理构建文件
	@echo "清理构建文件..."
	@rm -f $(BINARY_NAME)
	@rm -rf bin/
	@rm -rf temp/
	@echo "清理完成"

# =============================================================================
# Docker 相关
# =============================================================================

.PHONY: docker-build
docker-build: ## 构建 Docker 镜像
	@echo "构建 Docker 镜像..."
	@docker build -t $(DOCKER_NAME):$(VERSION) -t $(DOCKER_NAME):latest .
	@echo "Docker 镜像构建完成: $(DOCKER_NAME):$(VERSION)"

.PHONY: docker-build-local
docker-build-local: build-linux ## 使用本地构建的二进制文件构建 Docker 镜像
	@echo "使用本地二进制文件构建 Docker 镜像..."
	@echo "FROM alpine:latest" > Dockerfile.local
	@echo "RUN apk --no-cache add ca-certificates tzdata wget" >> Dockerfile.local
	@echo "ENV TZ=Asia/Shanghai" >> Dockerfile.local
	@echo "RUN addgroup -g 1001 -S saga && adduser -u 1001 -S saga -G saga" >> Dockerfile.local
	@echo "WORKDIR /app" >> Dockerfile.local
	@echo "COPY $(BINARY_NAME) ./saga" >> Dockerfile.local
	@echo "COPY manifest ./manifest" >> Dockerfile.local
	@echo "RUN chown -R saga:saga /app && chmod +x ./saga" >> Dockerfile.local
	@echo "USER saga" >> Dockerfile.local
	@echo "EXPOSE 8080" >> Dockerfile.local
	@echo "HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 CMD wget --no-verbose --tries=1 --spider http://localhost:8080/hello || exit 1" >> Dockerfile.local
	@echo "CMD [\"./saga\"]" >> Dockerfile.local
	@docker build -f Dockerfile.local -t $(DOCKER_NAME):$(VERSION) -t $(DOCKER_NAME):latest .
	@rm -f Dockerfile.local
	@echo "Docker 镜像构建完成: $(DOCKER_NAME):$(VERSION)"

.PHONY: docker-clean
docker-clean: ## 清理 Docker 镜像
	@echo "清理 Docker 镜像..."
	@docker rmi $(DOCKER_NAME):latest $(DOCKER_NAME):$(VERSION) 2>/dev/null || true
	@docker system prune -f
	@echo "Docker 清理完成"

# =============================================================================
# 部署相关
# =============================================================================

.PHONY: up
up: ## 启动服务（轻量级配置）
	@echo "启动服务（轻量级配置）..."
	@docker-compose -f docker-compose.light.yml up -d
	@echo "服务已启动，端口: 8080"

.PHONY: up-perf
up-perf: ## 启动服务（性能测试配置）
	@echo "启动服务（性能测试配置）..."
	@docker-compose up -d
	@echo "服务已启动，端口: 8080"

.PHONY: down
down: ## 停止服务
	@echo "停止服务..."
	@docker-compose -f docker-compose.light.yml down 2>/dev/null || docker-compose down
	@echo "服务已停止"

.PHONY: restart
restart: down up ## 重启服务

.PHONY: logs
logs: ## 查看应用日志
	@docker logs -f saga-app-light 2>/dev/null || docker logs -f saga-app

.PHONY: status
status: ## 查看服务状态
	@echo "服务状态:"
	@docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# =============================================================================
# 开发相关
# =============================================================================

.PHONY: test
test: ## 运行测试
	@echo "运行测试..."
	@go test -v ./...

.PHONY: test-coverage
test-coverage: ## 运行测试并生成覆盖率报告
	@echo "运行测试并生成覆盖率报告..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

.PHONY: fmt
fmt: ## 格式化代码
	@echo "格式化代码..."
	@go fmt ./...
	@echo "代码格式化完成"

.PHONY: lint
lint: ## 代码检查
	@echo "代码检查..."
	@golangci-lint run ./... 2>/dev/null || echo "请安装 golangci-lint: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"

.PHONY: deps
deps: ## 更新依赖
	@echo "更新依赖..."
	@go mod tidy
	@go mod download
	@echo "依赖更新完成"

# =============================================================================
# 性能测试相关
# =============================================================================

.PHONY: perf-test
perf-test: ## 运行性能测试
	@echo "运行性能测试..."
	@cd performance-test && ./run-tests.sh

.PHONY: perf-create-pool
perf-create-pool: ## 创建 Saga 事务池
	@echo "创建 Saga 事务池..."
	@cd performance-test && ./create-saga-pool.sh 200

.PHONY: perf-optimized
perf-optimized: ## 运行优化的性能测试
	@echo "运行优化的性能测试..."
	@export SAGA_POOL_FILE="performance-test/results/saga_pool_$$(ls -t performance-test/results/saga_pool_*.txt | head -1 | xargs basename)" && \
	wrk -t8 -c50 -d60s --latency -s performance-test/pool-based-test.lua http://localhost:8080

# =============================================================================
# 快捷命令
# =============================================================================

.PHONY: dev
dev: clean build up ## 开发环境快速启动

.PHONY: prod
prod: clean docker-build-local up ## 生产环境构建和部署

.PHONY: health
health: ## 健康检查
	@echo "健康检查..."
	@curl -s http://localhost:8080/hello || echo "服务未启动"

.PHONY: all
all: clean test build docker-build ## 完整构建流程
