#!/bin/bash

# VSCode Go 测试配置验证脚本

echo "🧪 VSCode Go 测试配置验证"
echo "=========================="

# 检查 Go 环境
echo "📋 检查 Go 环境..."
go version
echo ""

# 检查配置文件
echo "📁 检查 VSCode 配置文件..."
if [ -f ".vscode/settings.json" ]; then
    echo "✅ settings.json 存在"
else
    echo "❌ settings.json 不存在"
fi

if [ -f ".vscode/launch.json" ]; then
    echo "✅ launch.json 存在"
else
    echo "❌ launch.json 不存在"
fi

if [ -f ".vscode/tasks.json" ]; then
    echo "✅ tasks.json 存在"
else
    echo "❌ tasks.json 不存在"
fi
echo ""

# 测试默认参数
echo "🔧 测试默认参数 (-v -race -count=1 -timeout=30s)..."
echo "运行命令: go test -v -race -count=1 -timeout=30s ./internal/service -run TestSagaTransactionsService_DefaultRetryConfig"
go test -v -race -count=1 -timeout=30s ./internal/service -run TestSagaTransactionsService_DefaultRetryConfig
echo ""

# 测试覆盖率
echo "📊 测试覆盖率功能..."
echo "运行命令: go test -v -race -count=1 -timeout=30s -cover ./internal/service -run TestSagaTransactionsService_DefaultRetryConfig"
go test -v -race -count=1 -timeout=30s -cover ./internal/service -run TestSagaTransactionsService_DefaultRetryConfig
echo ""

# 测试基准测试参数
echo "⚡ 测试基准测试参数..."
echo "运行命令: go test -v -race -bench=BenchmarkSagaTransactions -benchmem -count=1 -timeout=1m ./internal/service"
# 注意：这里可能没有基准测试，所以可能会显示 "no tests to run"
go test -v -race -bench=BenchmarkSagaTransactions -benchmem -count=1 -timeout=1m ./internal/service 2>/dev/null || echo "ℹ️  没有找到基准测试（这是正常的）"
echo ""

# 清理测试缓存
echo "🧹 清理测试缓存..."
go clean -testcache
echo "✅ 测试缓存已清理"
echo ""

# 显示配置摘要
echo "📋 配置摘要"
echo "============"
echo "✅ 默认测试参数: -v -race -count=1 -timeout=30s"
echo "✅ 覆盖率支持: -cover -coverprofile=coverage.out"
echo "✅ 基准测试参数: -bench=. -benchmem -count=3"
echo "✅ 竞态检测: -race"
echo "✅ 详细输出: -v"
echo "✅ 禁用缓存: -count=1"
echo ""

echo "🎉 VSCode Go 测试配置验证完成！"
echo ""
echo "💡 使用方法："
echo "1. 在测试文件中点击函数上方的 'run test' 链接"
echo "2. 使用 Cmd+Shift+P -> 'Go: Test Package'"
echo "3. 使用调试面板 (Cmd+Shift+D) 选择测试配置"
echo "4. 使用任务 (Cmd+Shift+P -> 'Tasks: Run Task')"
echo ""
echo "📚 详细说明请查看: .vscode/README.md"
