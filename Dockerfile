# Saga 分布式事务系统 Dockerfile
# 优化的构建配置，支持本地构建和 Docker 构建

# 构建阶段
FROM golang:1.23-alpine AS builder

WORKDIR /app

# 设置构建环境
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# 安装构建依赖
RUN apk add --no-cache git ca-certificates tzdata

# 复制依赖文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN go build \
    -ldflags="-w -s -X main.Version=docker \
              -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S) \
              -X main.GitCommit=$(git rev-parse --short HEAD 2>/dev/null || echo 'docker-build')" \
    -o saga \
    main.go

# 运行阶段
FROM alpine:latest

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata wget

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非 root 用户
RUN addgroup -g 1001 -S saga && \
    adduser -u 1001 -S saga -G saga

WORKDIR /app

# 从构建阶段复制文件
COPY --from=builder /app/saga ./saga
COPY --from=builder /app/manifest ./manifest

# 设置权限
RUN chown -R saga:saga /app && \
    chmod +x ./saga

# 切换到非 root 用户
USER saga

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/hello || exit 1

# 启动应用
CMD ["./saga"]
