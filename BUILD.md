# Saga 分布式事务系统 - 构建指南

## 🚀 快速开始

### 1. 环境要求
- Go 1.23.0+
- Docker (可选，用于数据库和容器化部署)
- Make

### 2. 快速启动
```bash
# 查看所有可用命令
make help

# 启动完整开发环境（数据库+应用）
make start

# 或者分步启动
make db-start    # 启动数据库
make dev         # 启动应用
```

### 3. 构建项目
```bash
# 构建本地二进制文件
make build-local

# 构建所有平台的二进制文件
make build-all

# 使用 GoFrame CLI 构建（推荐）
make build
```

## 🛠 开发工具

### 代码质量检查
```bash
# 格式化代码
make fmt

# 运行所有检查（格式化、vet、lint、测试）
make check

# 单独运行测试
make test
make test-coverage
make test-race
```

### 依赖管理
```bash
# 下载依赖
make deps

# 更新依赖
make deps-update

# 查看依赖图
make mod-graph
```

## 🐳 Docker 支持

### 构建和运行 Docker 镜像
```bash
# 构建 Docker 镜像
make docker-build

# 运行 Docker 容器
make docker-run

# 清理 Docker 资源
make docker-clean
```

## 🗄️ 数据库管理

### 数据库操作
```bash
# 启动 MySQL 数据库
make db-start

# 连接到数据库
make db-shell

# 查看数据库日志
make db-logs

# 重置数据库
make db-reset

# 停止数据库
make db-stop
```

## 📊 性能测试

### 安装性能测试工具
```bash
# 安装 wrk、hey 等工具
make perf-install
```

### 运行性能测试
```bash
# 基础性能测试
make perf-test-basic

# 查询性能测试
make perf-test-query
```

## 🔧 GoFrame 相关命令

### 代码生成
```bash
# 生成 DAO/DO/Entity 文件
make dao

# 生成控制器文件
make ctrl

# 生成服务文件
make service

# 更新 GoFrame 到最新版本
make up
```

## 📦 部署

### 本地安装
```bash
# 安装到 /usr/local/bin
make install

# 从本地卸载
make uninstall
```

### 服务状态检查
```bash
# 查看服务状态
make status

# 查看版本信息
make version
```

## 🎯 常用工作流

### 开发流程
```bash
# 1. 启动开发环境
make start

# 2. 开发代码...

# 3. 运行测试
make test

# 4. 代码检查
make check

# 5. 构建
make build-local
```

### 部署流程
```bash
# 1. 运行完整检查
make check

# 2. 构建 Docker 镜像
make docker-build

# 3. 推送镜像（如果需要）
make docker-push
```

## 🔍 故障排除

### 常见问题

1. **GoFrame CLI 未安装**
   ```bash
   make cli  # 自动安装 GoFrame CLI
   ```

2. **数据库连接失败**
   ```bash
   make db-start  # 确保数据库已启动
   make db-logs   # 查看数据库日志
   ```

3. **构建失败**
   ```bash
   make deps      # 重新下载依赖
   make clean     # 清理构建文件
   ```

4. **性能测试工具缺失**
   ```bash
   make perf-install  # 安装性能测试工具
   ```

## 📝 配置文件

主要配置文件位置：
- `hack/config.yaml` - GoFrame 和应用配置
- `Dockerfile` - Docker 构建配置
- `Makefile` - 构建和开发工具配置

## 🎉 完成！

现在你可以使用 `make help` 查看所有可用命令，开始开发 Saga 分布式事务系统了！
