# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# Build artifacts
bin/
dist/
temp/
coverage.out
coverage.html

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Test files
*_test.go

# Development
.env
.env.local
.env.development
.env.test
.env.production

# Dependencies (will be downloaded in container)
vendor/

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Performance testing
scripts/perf/
