package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SagaStepTemplate 步骤模板定义（简化版本）
type SagaStepTemplate struct {
	StepIndex   int    `json:"step_index"`  // 步骤索引
	Service     string `json:"service"`     // 服务名称
	Action      string `json:"action"`      // 动作名称
	Description string `json:"description"` // 描述
}

// ==================== CreateSagaTransaction ====================

type CreateSagaTransactionReq struct {
	g.Meta        `path:"/saga/transactions" tags:"Saga事务" method:"post" summary:"创建分布式事务"`
	Name          string             `json:"name"           v:"required|length:1,100#Saga名称不能为空|Saga名称长度为1-100个字符"`
	StepIndexMode string             `json:"stepIndexMode"  v:"in:auto,manual#StepIndex管理模式无效"` // StepIndex管理模式
	StepTemplates []SagaStepTemplate `json:"stepTemplates"`                                     // 步骤模板（仅manual模式使用）
}

type CreateSagaTransactionRes struct {
	g.Meta    `mime:"application/json"`
	SagaId    string      `json:"sagaId"`    // Saga 事务 ID
	Name      string      `json:"name"`      // Saga 名称
	Status    string      `json:"status"`    // 当前状态
	CreatedAt *gtime.Time `json:"createdAt"` // 创建时间
}

// ==================== ReportCompensation ====================

type ReportCompensationReq struct {
	g.Meta              `path:"/saga/transactions/compensation" tags:"Saga事务" method:"post" summary:"上报补偿操作结果"`
	SagaId              string `json:"sagaId"              v:"required|length:1,50#SagaId不能为空|SagaId长度为1-50个字符"`
	Action              string `json:"action"              v:"required|length:1,100#步骤名称不能为空|步骤名称长度为1-100个字符"`
	ServiceName         string `json:"serviceName"         v:"required|length:1,100#服务名称不能为空|服务名称长度为1-100个字符"`
	ContextData         string `json:"contextData"`                                                // 正向执行的上下文参数
	CompensationContext string `json:"compensationContext" v:"required|json#补偿需要的上下文参数不能为空"`       // 补偿需要的上下文参数
	CompensateEndpoint  string `json:"compensateEndpoint"  v:"max-length:500#补偿接口URL长度不能超过500个字符"` // 补偿接口 URL
}

type ReportCompensationRes struct {
	g.Meta  `mime:"application/json"`
	Success bool `json:"success"` // 是否成功
}

// ==================== GetSagaTransaction ====================

type GetSagaTransactionReq struct {
	g.Meta `path:"/saga/transactions/{sagaId}" tags:"Saga事务" method:"get" summary:"获取Saga事务信息"`
	SagaId string `json:"sagaId" v:"required|length:1,50#SagaId不能为空|SagaId长度为1-50个字符"`
}

type GetSagaTransactionRes struct {
	g.Meta      `mime:"application/json"`
	SagaId      string      `json:"sagaId"`      // Saga 事务 ID
	Name        string      `json:"name"`        // Saga 名称
	Status      string      `json:"status"`      // 当前状态
	CurrentStep string      `json:"currentStep"` // 当前步骤
	RetryCount  int         `json:"retryCount"`  // 重试次数
	CreatedAt   *gtime.Time `json:"createdAt"`   // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"`   // 更新时间
}

// ==================== CommitSagaTransaction ====================

type CommitSagaTransactionReq struct {
	g.Meta `path:"/saga/transactions/commit" tags:"Saga事务" method:"post" summary:"提交分布式事务"`
	SagaId string `json:"sagaId" v:"required|length:1,50#SagaId不能为空|SagaId长度为1-50个字符"`
}

type CommitSagaTransactionRes struct {
	g.Meta         `mime:"application/json"`
	Success        bool        `json:"success"`        // 是否成功
	Message        string      `json:"message"`        // 响应消息
	SagaId         string      `json:"sagaId"`         // Saga 事务 ID
	CompletedSteps int         `json:"completedSteps"` // 已完成步骤数
	ExpectedSteps  int         `json:"expectedSteps"`  // 预期步骤数
	NewStatus      string      `json:"newStatus"`      // 新的状态
	CompletedAt    *gtime.Time `json:"completedAt"`    // 完成时间
}

// ==================== RollBackSagaTransaction ====================

type RollBackSagaTransactionReq struct {
	g.Meta        `path:"/saga/transactions/rollback" tags:"Saga事务" method:"post" summary:"回滚分布式事务"`
	SagaId        string `json:"sagaId"              v:"required|length:1,50#SagaId不能为空|SagaId长度为1-50个字符"`
	FailReason    string `json:"failReason"          v:"required|length:1,500#回滚原因不能为空|回滚原因长度为1-500个字符"`
	FailedStep    string `json:"failedStep"          v:"max-length:100#失败的步骤名称长度不能超过100个字符"`        // 失败的步骤名称（可选）
	ExecutionMode string `json:"executionMode"       v:"in:none,sync,async#执行模式必须为none、sync或async"` // 执行模式：none/sync/async
}

type RollBackSagaTransactionRes struct {
	g.Meta              `mime:"application/json"`
	Success             bool        `json:"success"`             // 是否成功启动回滚
	Message             string      `json:"message"`             // 响应消息
	SagaId              string      `json:"sagaId"`              // Saga 事务 ID
	NewStatus           string      `json:"newStatus"`           // 新的状态
	IsRollbackCompleted bool        `json:"isRollbackCompleted"` // 回滚是否完成
	StartedAt           *gtime.Time `json:"startedAt"`           // 回滚开始时间
	CompletedAt         *gtime.Time `json:"completedAt"`         // 回滚完成时间（如果已完成）
	FailReason          string      `json:"failReason"`          // 失败原因
}
