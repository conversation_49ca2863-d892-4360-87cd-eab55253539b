// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package saga_transactions

import (
	"context"

	"saga/api/saga_transactions/v1"
)

type ISagaTransactionsV1 interface {
	CreateSagaTransaction(ctx context.Context, req *v1.CreateSagaTransactionReq) (res *v1.CreateSagaTransactionRes, err error)
	ReportCompensation(ctx context.Context, req *v1.ReportCompensationReq) (res *v1.ReportCompensationRes, err error)
	GetSagaTransaction(ctx context.Context, req *v1.GetSagaTransactionReq) (res *v1.GetSagaTransactionRes, err error)
	CommitSagaTransaction(ctx context.Context, req *v1.CommitSagaTransactionReq) (res *v1.CommitSagaTransactionRes, err error)
}
